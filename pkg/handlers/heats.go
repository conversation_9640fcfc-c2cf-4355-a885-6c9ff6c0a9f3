package handlers

import (
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// CreateHeat handles creating a new heat for an event
func (h *Handler) CreateHeat(c *gin.Context) {
	idStr := c.Param("id")
	eventID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	// Get the event to check if it's entypsegling
	event, err := h.DB.GetEvent(eventID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get event details"})
		return
	}

	// Get form data
	heatNumberStr := c.PostForm("heat_number")
	name := c.PostForm("name")
	startTime := c.PostForm("start_time")

	heatNumber, err := strconv.Atoi(heatNumberStr)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid heat number"})
		return
	}

	// For entypsegling events, start time is not needed
	if event.Entypsegling {
		startTime = "" // No start time for entypsegling events
	} else {
		// If no start time provided for regular events, use a default
		if startTime == "" {
			startTime = "10:00"
		}
	}

	// Create the heat
	_, err = h.DB.CreateHeat(eventID, heatNumber, name, startTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Check if we should redirect to a different page
	redirectTo := c.Query("redirect")
	if redirectTo == "results" {
		c.Redirect(http.StatusSeeOther, "/events/"+idStr+"/results")
	} else if redirectTo == "finish-times" {
		c.Redirect(http.StatusSeeOther, "/events/"+idStr+"/finish-times")
	} else {
		// Default: redirect back to the heats page
		c.Redirect(http.StatusSeeOther, "/events/"+idStr+"/heats")
	}
}

// DeleteHeat handles deleting a heat
func (h *Handler) DeleteHeat(c *gin.Context) {
	idStr := c.Param("id")
	log.Printf("DEBUG: DeleteHeat called with ID: %s, Method: %s", idStr, c.Request.Method)

	heatID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid heat ID: %s", idStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid heat ID"})
		return
	}

	// Delete the heat
	err = h.DB.DeleteHeat(heatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return success for AJAX calls (no redirect needed)
	c.JSON(http.StatusOK, gin.H{"message": "Heat deleted successfully"})
}

// GetEventHeatsAPI returns heats for an event as JSON
func (h *Handler) GetEventHeatsAPI(c *gin.Context) {
	idStr := c.Param("id")
	eventID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	heats, err := h.DB.GetEventHeats(eventID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, heats)
}

// UpdateHeatStartTime handles updating the start time for a heat
func (h *Handler) UpdateHeatStartTime(c *gin.Context) {
	heatIDStr := c.Param("heat_id")
	heatID, err := strconv.ParseInt(heatIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid heat ID"})
		return
	}

	startTime := c.PostForm("start_time")
	if startTime == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Start time is required"})
		return
	}

	err = h.DB.UpdateHeatStartTime(heatID, startTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Optional redirect back to finish times if requested
	redirect := c.DefaultQuery("redirect", "")
	if redirect == "finish-times" {
		// Find the event ID from the heat
		heat, _ := h.DB.GetHeat(heatID)
		c.Redirect(http.StatusSeeOther, fmt.Sprintf("/events/%d/finish-times?heat=%d", heat.EventID, heatID))
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Start time updated successfully"})
}

// UpdateHeatDetails handles updating number, name and start time for a heat
func (h *Handler) UpdateHeatDetails(c *gin.Context) {
	heatIDStr := c.Param("heat_id")
	heatID, err := strconv.ParseInt(heatIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid heat ID"})
		return
	}

	heatNumberStr := c.PostForm("heat_number")
	name := c.PostForm("name")
	startTime := c.PostForm("start_time")

	if heatNumberStr == "" || name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Nummer och namn krävs"})
		return
	}

	heatNumber, err := strconv.Atoi(heatNumberStr)
	if err != nil || heatNumber < 1 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Ogiltigt deltävlingnummer"})
		return
	}

	// Get event to know if entypsegling (no start time)
	heat, err := h.DB.GetHeat(heatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte hämta deltävling"})
		return
	}
	event, err := h.DB.GetEvent(heat.EventID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte hämta tävling"})
		return
	}

	var startTimePtr *string
	if !event.Entypsegling {
		// Allow empty to clear; otherwise must be HH:MM
		if startTime != "" && len(startTime) != 5 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Starttid måste vara i format HH:MM"})
			return
		}
		startTimePtr = &startTime
	}

	if err := h.DB.UpdateHeatDetails(heatID, heatNumber, name, startTimePtr); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	redirect := c.DefaultQuery("redirect", "")
	if redirect == "finish-times" {
		c.Redirect(http.StatusSeeOther, fmt.Sprintf("/events/%d/finish-times?heat=%d", heat.EventID, heatID))
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Deltävling uppdaterad"})
}

// UpdateHeatFinishTime handles updating finish time for a participant in a specific heat
func (h *Handler) UpdateHeatFinishTime(c *gin.Context) {
	heatIDStr := c.Param("heat_id")
	participantIDStr := c.Param("participant_id")

	log.Printf("DEBUG: UpdateHeatFinishTime called with heat_id=%s, participant_id=%s", heatIDStr, participantIDStr)

	heatID, err := strconv.ParseInt(heatIDStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid heat ID: %s", heatIDStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid heat ID"})
		return
	}

	participantID, err := strconv.ParseInt(participantIDStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid participant ID: %s", participantIDStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid participant ID"})
		return
	}

	// Get form data
	finishTime := c.PostForm("finish_time_" + participantIDStr)
	dns := c.PostForm("dns_"+participantIDStr) == "on"
	dnf := c.PostForm("dnf_"+participantIDStr) == "on"

	log.Printf("DEBUG: Form data - finish_time_%s=%s, dns_%s=%v, dnf_%s=%v",
		participantIDStr, finishTime, participantIDStr, dns, participantIDStr, dnf)

	// Update the heat finish time
	err = h.DB.UpdateHeatFinishTime(heatID, participantID, finishTime, dns, dnf)
	if err != nil {
		log.Printf("ERROR: Failed to update heat finish time: %v", err)
		c.HTML(http.StatusInternalServerError, "finish_time_status.html", gin.H{
			"status":  "error",
			"message": err.Error(),
		})
		return
	}

	log.Printf("SUCCESS: Updated heat finish time for heat %d, participant %d", heatID, participantID)
	c.HTML(http.StatusOK, "finish_time_status.html", gin.H{
		"status":  "success",
		"message": "Måltid uppdaterad",
	})
}

// GetHeatResults handles displaying results for a specific heat
func (h *Handler) GetHeatResults(c *gin.Context) {
	idStr := c.Param("id")
	heatID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid heat ID",
		})
		return
	}

	// Get heat results
	results, err := h.DB.GetHeatResultsComplete(heatID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get the heat information
	heat, err := h.DB.GetHeat(heatID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get the event
	event, err := h.DB.GetEvent(heat.EventID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	c.HTML(http.StatusOK, "heat_results.html", gin.H{
		"title":      "Resultat - " + heat.Name,
		"event":      event,
		"heat":       heat,
		"results":    results,
		"use24hTime": h.getTimeFormatSetting(),
	})
}

// UpdateHeatPlacement handles updating placement for entypsegling events
func (h *Handler) UpdateHeatPlacement(c *gin.Context) {
	// Get heat ID from URL
	heatIDStr := c.Param("heat_id")
	heatID, err := strconv.ParseInt(heatIDStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid heat ID: %s", heatIDStr)
		c.HTML(http.StatusBadRequest, "finish_time_status.html", gin.H{
			"status":  "error",
			"message": "Invalid heat ID",
		})
		return
	}

	// Get participant ID from URL
	participantIDStr := c.Param("participant_id")
	participantID, err := strconv.ParseInt(participantIDStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid participant ID: %s", participantIDStr)
		c.HTML(http.StatusBadRequest, "finish_time_status.html", gin.H{
			"status":  "error",
			"message": "Invalid participant ID",
		})
		return
	}

	// Get placement from form
	placementStr := c.PostForm("placement_" + participantIDStr)
	dns := c.PostForm("dns_"+participantIDStr) == "on"
	dnf := c.PostForm("dnf_"+participantIDStr) == "on"

	log.Printf("DEBUG: Form data - placement_%s=%s, dns_%s=%v, dnf_%s=%v",
		participantIDStr, placementStr, participantIDStr, dns, participantIDStr, dnf)

	// Parse placement
	var placement int
	if placementStr != "" && !dns && !dnf {
		placement, err = strconv.Atoi(placementStr)
		if err != nil || placement < 1 {
			log.Printf("ERROR: Invalid placement: %s", placementStr)
			c.HTML(http.StatusBadRequest, "finish_time_status.html", gin.H{
				"status":  "error",
				"message": "Invalid placement - must be a positive number",
			})
			return
		}
	}

	// Update the heat placement
	err = h.DB.UpdateHeatPlacement(heatID, participantID, placement, dns, dnf)
	if err != nil {
		log.Printf("ERROR: Failed to update heat placement: %v", err)
		c.HTML(http.StatusInternalServerError, "finish_time_status.html", gin.H{
			"status":  "error",
			"message": err.Error(),
		})
		return
	}

	log.Printf("DEBUG: Successfully updated heat placement for participant %d in heat %d to %d", participantID, heatID, placement)

	// Return success message
	c.HTML(http.StatusOK, "finish_time_status.html", gin.H{
		"status": "success",
		"DNS":    dns,
		"DNF":    dnf,
	})
}
