{{ template "header.html" . }}

<style>
    .disabled-form {
        opacity: 0.7;
        pointer-events: none;
    }

    .disabled-form input,
    .disabled-form select,
    .disabled-form textarea,
    .disabled-form button {
        pointer-events: none;
    }

    a[disabled] {
        pointer-events: none;
        opacity: 0.65;
    }
</style>

<div class="container mt-4">
    {{ if .event.Entypsegling }}
    <h1>Placeringar - {{ .event.Namn }}</h1>
    {{ else }}
    <h1><PERSON><PERSON><PERSON>ider - {{ .event.Namn }}</h1>
    {{ end }}

    <div class="row">
        <div class="col-md-3">
            {{ template "event_nav.html" . }}
        </div>
        <div class="col-md-9">
            <div class="row mb-3">
                <div class="col-md-12">
                    {{ if .event.Entypsegling }}
                    <p>
                        <strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} |
                        <strong>B<PERSON>ttyp:</strong> {{ .event.BoatType }} |
                        <strong>Vind:</strong> {{ .event.Vind }} m/s
                    </p>
                    {{ else }}
                    <p>
                        <strong>Datum:</strong> {{ .event.Datum.Format "2006-01-02" }} |
                        <strong>Starttid:</strong> {{ .event.Starttid }} |
                        <strong>Vind:</strong> {{ .event.Vind }} m/s |
                        <strong>Banlängd:</strong> {{ .event.Banlangd }} nm
                    </p>
                    {{ end }}
                </div>
            </div>

            {{if gt (len .heats) 1}}
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>Välj deltävling</h5>
                        </div>
                        <div class="card-body">
                            <div class="btn-group" role="group" aria-label="Heat selection">
                                {{range .heats}}
                                <a href="/events/{{$.event.ID}}/finish-times?heat={{.ID}}"
                                   class="btn {{if eq .ID $.selectedHeat.ID}}btn-primary{{else}}btn-outline-primary{{end}}">
                                    {{.Name}}
                                    {{if .StartTime}}<br><small>{{.StartTime}}</small>{{end}}
                                </a>
                                {{end}}
                            </div>
                            <div class="mt-2 d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    {{ if $.event.Entypsegling }}
                                    Visar placeringar för: <strong>{{.selectedHeat.Name}}</strong>
                                    {{ else }}
                                    Visar måltider för: <strong>{{.selectedHeat.Name}}</strong>
                                    {{if .selectedHeat.StartTime}}(Starttid: {{.selectedHeat.StartTime}}){{end}}
                                    {{ end }}
                                </small>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addHeatModal">
                                        <i class="fas fa-plus"></i> Lägg till deltävling
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#editHeatModal">
                                        <i class="fas fa-pen"></i> Redigera deltävling
                                    </button>
                                    {{if gt (len .heats) 1}}
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteCurrentHeat()">
                                        <i class="fas fa-trash"></i> Ta bort denna deltävling
                                    </button>
                                    {{end}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{else if .selectedHeat.ID}}
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="alert alert-info d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-info-circle"></i>
                            {{ if .event.Entypsegling }}
                            Visar placeringar för: <strong>{{.selectedHeat.Name}}</strong>
                            {{ else }}
                            Visar måltider för: <strong>{{.selectedHeat.Name}}</strong>
                            {{if .selectedHeat.StartTime}}(Starttid: {{.selectedHeat.StartTime}}){{end}}
                            {{ end }}
                        </div>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addHeatModal">
                                <i class="fas fa-plus"></i> Lägg till deltävling
                            </button>
                            <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#editHeatModal">
                                <i class="fas fa-pen"></i> Redigera deltävling
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {{end}}

            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        {{ if .event.Entypsegling }}
                        <h2>Registrera placeringar</h2>
                        {{ else }}
                        <h2>Registrera måltider</h2>
                        {{ end }}
                    </div>
                    <div class="d-flex align-items-center">
                        {{ if .event.Entypsegling }}
                        <p class="text-muted mb-0 me-2">Ange placeringen för varje deltagare (1:a, 2:a, 3:e, etc.)</p>
                        {{ else }}
                        <p class="text-muted mb-0 me-2">Ange tiden när båten passerar mållinjen</p>
                        {{ end }}
                        <!-- TEMPORARILY DISABLED: Lock status badge -->
                        <!-- {{ if .event.Locked }}
                        <span class="badge bg-danger">Låst</span>
                        {{ end }} -->
                    </div>
                </div>
                <div class="card-body">
                    <!-- TEMPORARILY DISABLED: Lock warning alert -->
                    <!-- {{ if .event.Locked }}
                    <div class="alert alert-warning mb-4">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        För att låsa upp, gå till <a href="/events/{{ .event.ID }}/results">Resultat</a>
                    </div>
                    {{ end }} -->
                    {{ if .participants }}
                    {{ if .event.Entypsegling }}
                    <!-- Entypsegling placement form -->
                    <form id="placement-form" hx-post="/events/{{ .event.ID }}/finish-times" hx-swap="none">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Seglare</th>
                                        <th>Segelnummer</th>
                                        <th>Gastar</th>
                                        <th>Placering</th>
                                        <th>Status</th>
                                        <th>DNS/DNF</th>
                                    </tr>
                                </thead>
                    {{ else }}
                    <!-- Regular finish times form -->
                    <form id="finish-times-form" hx-post="/events/{{ .event.ID }}/finish-times" hx-swap="none">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Seglare</th>
                                        <th>Båt</th>
                                        <th>SRS-värde</th>
                                        {{ if .event.Jaktstart }}
                                        <th>Starttid</th>
                                        {{ end }}
                                        <th>Måltid (hh:mm:ss)</th>
                                        <th>Status</th>
                                        <th>DNS/DNF</th>
                                    </tr>
                                </thead>
                    {{ end }}
                                <tbody>
                                    {{ range .participants }}
                                    <tr id="participant-{{ .ID }}">
                                        <td>{{ .Sailor.Namn }}</td>
                                        {{ if $.event.Entypsegling }}
                                        <td>
                                            <strong>{{ .PersonalNumber }}</strong>
                                        </td>
                                        <td>{{ .CrewCount }}</td>
                                        {{ else }}
                                        <td>
                                            {{ .Boat.Namn }} ({{ .Boat.Battyp }})
                                            {{ if or .Boat.Segelnummer .Boat.Nationality }}
                                                <br>
                                                <small class="text-muted">
                                                    {{ if and .Boat.Nationality .Boat.Segelnummer }}
                                                        {{ .Boat.Nationality }}-{{ .Boat.Segelnummer }}
                                                    {{ else if .Boat.Segelnummer }}
                                                        {{ .Boat.Segelnummer }}
                                                    {{ else if .Boat.Nationality }}
                                                        {{ .Boat.Nationality }}
                                                    {{ end }}
                                                </small>
                                            {{ end }}
                                        </td>
                                        <td>
                                            {{ if .UseCustomSRSValue }}
                                                {{ .CustomSRSValue }} (anpassad)
                                            {{ else }}
                                                {{ .SelectedSRSValue }}
                                                {{ if eq .SRSType "srs" }}
                                                    (SRS)
                                                {{ else if eq .SRSType "srs_utan_undanvindsegel" }}
                                                    (SRS utan undanvindsegel)
                                                {{ else if eq .SRSType "srs_shorthanded" }}
                                                    (SRS S/H)
                                                {{ else if eq .SRSType "srs_shorthanded_utan_undanvindsegel" }}
                                                    (SRS S/H utan undanvindsegel)
                                                {{ end }}
                                            {{ end }}
                                        </td>
                                        {{ end }}
                                        {{ if $.event.Entypsegling }}
                                        <td>
                                            {{$heatFinishTime := index $.heatFinishTimeMap .ID}}
                                            <input type="number"
                                                min="1"
                                                class="form-control placement-input"
                                                name="placement_{{ .ID }}"
                                                id="placement_{{ .ID }}"
                                                value="{{if $heatFinishTime.Placement}}{{$heatFinishTime.Placement}}{{end}}"
                                                placeholder="1, 2, 3..."
                                                {{if gt $.selectedHeat.ID 0}}hx-post="/heats/{{$.selectedHeat.ID}}/placements/{{ .ID }}"{{else}}hx-post="/events/{{$.event.ID}}/finish-times/{{ .ID }}"{{end}}
                                                hx-trigger="change"
                                                hx-target="#status-{{ .ID }}"
                                                hx-swap="innerHTML"
                                                hx-headers='{"X-Event-ID": "{{ $.event.ID }}"}'
                                                {{ if or $heatFinishTime.DNS $heatFinishTime.DNF }}disabled{{ end }}>
                                        </td>
                                        <td id="status-{{ .ID }}">
                                            {{$heatFinishTime := index $.heatFinishTimeMap .ID}}
                                            {{ if $heatFinishTime.Placement }}
                                            <span class="badge bg-success">{{ $heatFinishTime.Placement }}:a plats</span>
                                            {{ else if $heatFinishTime.DNS }}
                                            <span class="badge bg-warning">DNS</span>
                                            {{ else if $heatFinishTime.DNF }}
                                            <span class="badge bg-danger">DNF</span>
                                            {{ else }}
                                            <span class="badge bg-secondary">Väntar</span>
                                            {{ end }}
                                        </td>
                                        <td>
                                            {{$heatFinishTime := index $.heatFinishTimeMap .ID}}
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input dns-checkbox"
                                                    type="checkbox"
                                                    id="dns_{{ .ID }}"
                                                    name="dns_{{ .ID }}"
                                                    {{ if $heatFinishTime.DNS }}checked{{ end }}
                                                    {{if gt $.selectedHeat.ID 0}}hx-post="/heats/{{$.selectedHeat.ID}}/placements/{{ .ID }}"{{else}}hx-post="/events/{{$.event.ID}}/finish-times/{{ .ID }}/dns"{{end}}
                                                    hx-trigger="change"
                                                    hx-target="#status-{{ .ID }}"
                                                    hx-swap="innerHTML"
                                                    hx-include="[name='dnf_{{ .ID }}']">
                                                <label class="form-check-label" for="dns_{{ .ID }}">DNS</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input dnf-checkbox"
                                                    type="checkbox"
                                                    id="dnf_{{ .ID }}"
                                                    name="dnf_{{ .ID }}"
                                                    {{ if $heatFinishTime.DNF }}checked{{ end }}
                                                    {{if gt $.selectedHeat.ID 0}}hx-post="/heats/{{$.selectedHeat.ID}}/placements/{{ .ID }}"{{else}}hx-post="/events/{{$.event.ID}}/finish-times/{{ .ID }}/dnf"{{end}}
                                                    hx-trigger="change"
                                                    hx-target="#status-{{ .ID }}"
                                                    hx-swap="innerHTML"
                                                    hx-include="[name='dns_{{ .ID }}']">
                                                <label class="form-check-label" for="dnf_{{ .ID }}">DNF</label>
                                            </div>
                                        </td>
                                        {{ else }}
                                        {{ if $.event.Jaktstart }}
                                        <td>{{ .AbsoluteStartTime }}</td>
                                        {{ end }}
                                        <td>
                                            {{$heatFinishTime := index $.heatFinishTimeMap .ID}}
                                            <input type="time"
                                                class="form-control finish-time"
                                                name="finish_time_{{ .ID }}"
                                                id="finish_time_{{ .ID }}"
                                                step="1"
                                                value="{{if $heatFinishTime.FinishTime}}{{$heatFinishTime.FinishTime}}{{else}}{{.FinishTime}}{{end}}"
                                                {{if gt $.selectedHeat.ID 0}}hx-post="/heats/{{$.selectedHeat.ID}}/finish-times/{{ .ID }}"{{else}}hx-post="/events/{{$.event.ID}}/finish-times/{{ .ID }}"{{end}}
                                                hx-trigger="change"
                                                hx-target="#status-{{ .ID }}"
                                                hx-swap="innerHTML"
                                                hx-headers='{"X-Event-ID": "{{ $.event.ID }}"}'
                                                {{ if $.use24hTime }}data-force-24h="true"{{ end }}
                                                {{ if or $heatFinishTime.DNS $heatFinishTime.DNF }}disabled{{ end }}>
                                                <!-- TEMPORARILY DISABLED: {{ if $.event.Locked }}disabled{{ end }} -->
                                        </td>
                                        <td id="status-{{ .ID }}">
                                            {{$heatFinishTime := index $.heatFinishTimeMap .ID}}
                                            {{ if $heatFinishTime.FinishTime }}
                                            <span class="badge bg-success">Registrerad</span>
                                            {{ else if $heatFinishTime.DNS }}
                                            <span class="badge bg-warning">DNS</span>
                                            {{ else if $heatFinishTime.DNF }}
                                            <span class="badge bg-danger">DNF</span>
                                            {{ else }}
                                            <span class="badge bg-secondary">--:--:--</span>
                                            {{ end }}
                                        </td>
                                        <td>
                                            {{$heatFinishTime := index $.heatFinishTimeMap .ID}}
                                            <div class="form-check form-check-inline">
                                                <!-- TEMPORARILY DISABLED: {{ if $.event.Locked }}disabled{{ end }} -->
                                                <input class="form-check-input dns-checkbox"
                                                    type="checkbox"
                                                    id="dns_{{ .ID }}"
                                                    name="dns_{{ .ID }}"
                                                    {{ if $heatFinishTime.DNS }}checked{{ end }}
                                                    {{if gt $.selectedHeat.ID 0}}hx-post="/heats/{{$.selectedHeat.ID}}/finish-times/{{ .ID }}"{{else}}hx-post="/events/{{$.event.ID}}/finish-times/{{ .ID }}/dns"{{end}}
                                                    hx-trigger="change"
                                                    hx-target="#status-{{ .ID }}"
                                                    hx-swap="innerHTML"
                                                    hx-include="[name='dnf_{{ .ID }}']">
                                                <label class="form-check-label" for="dns_{{ .ID }}">DNS</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <!-- TEMPORARILY DISABLED: {{ if $.event.Locked }}disabled{{ end }} -->
                                                <input class="form-check-input dnf-checkbox"
                                                    type="checkbox"
                                                    id="dnf_{{ .ID }}"
                                                    name="dnf_{{ .ID }}"
                                                    {{ if $heatFinishTime.DNF }}checked{{ end }}
                                                    {{if gt $.selectedHeat.ID 0}}hx-post="/heats/{{$.selectedHeat.ID}}/finish-times/{{ .ID }}"{{else}}hx-post="/events/{{$.event.ID}}/finish-times/{{ .ID }}/dnf"{{end}}
                                                    hx-trigger="change"
                                                    hx-target="#status-{{ .ID }}"
                                                    hx-swap="innerHTML"
                                                    hx-include="[name='dns_{{ .ID }}']">
                                                <label class="form-check-label" for="dnf_{{ .ID }}">DNF</label>
                                            </div>
                                        </td>
                                        {{ end }}
                                    </tr>
                                    {{ end }}
                                </tbody>
                            </table>
                        </div>

                    </form>
                    {{ else }}
                    <p>Inga deltagare hittades.</p>
                    {{ end }}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Handle errors in HTMX requests
    document.addEventListener('htmx:responseError', function(event) {
        // Parse the error response
        let errorMessage = 'Okänt fel';
        try {
            const response = JSON.parse(event.detail.xhr.responseText);
            if (response.error) {
                errorMessage = response.error;
            }
        } catch (e) {
            // If parsing fails, use the raw response text
            errorMessage = event.detail.xhr.responseText || 'Okänt fel';
        }

        // Show an error message
        alert('Ett fel uppstod: ' + errorMessage);

        // Log the error details to console for debugging
        console.error('HTMX Error:', {
            status: event.detail.xhr.status,
            statusText: event.detail.xhr.statusText,
            responseText: event.detail.xhr.responseText,
            requestPath: event.detail.pathInfo.requestPath,
            target: event.detail.target ? event.detail.target.id : 'none'
        });
    });

    // Show success message after saving finish time
    document.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.successful && event.detail.target.id.startsWith('status-')) {
            // Flash the status cell with a green background
            const statusCell = event.detail.target;
            statusCell.classList.add('bg-success', 'bg-opacity-25');
            setTimeout(function() {
                statusCell.classList.remove('bg-success', 'bg-opacity-25');
            }, 1000);

            // Log success to console
            console.log('Successfully updated finish time for participant:',
                event.detail.target.id.replace('status-', ''));
        }
    });

    // Handle DNS/DNF checkbox interactions
    document.addEventListener('DOMContentLoaded', function() {
        // Function to handle DNS checkbox changes
        function handleDnsCheckboxChange(checkbox) {
            const participantId = checkbox.id.replace('dns_', '');
            const finishTimeInput = document.getElementById('finish_time_' + participantId);
            const placementInput = document.getElementById('placement_' + participantId);
            const dnfCheckbox = document.getElementById('dnf_' + participantId);

            if (checkbox.checked) {
                // If DNS is checked, disable inputs and uncheck DNF
                if (finishTimeInput) {
                    finishTimeInput.disabled = true;
                    finishTimeInput.value = '';
                }
                if (placementInput) {
                    placementInput.disabled = true;
                    placementInput.value = '';
                }
                if (dnfCheckbox.checked) {
                    dnfCheckbox.checked = false;
                }
            } else {
                // If DNS is unchecked, enable inputs
                if (finishTimeInput) {
                    finishTimeInput.disabled = false;
                }
                if (placementInput) {
                    placementInput.disabled = false;
                }
            }
        }

        // Function to handle DNF checkbox changes
        function handleDnfCheckboxChange(checkbox) {
            const participantId = checkbox.id.replace('dnf_', '');
            const finishTimeInput = document.getElementById('finish_time_' + participantId);
            const placementInput = document.getElementById('placement_' + participantId);
            const dnsCheckbox = document.getElementById('dns_' + participantId);

            if (checkbox.checked) {
                // If DNF is checked, disable inputs and uncheck DNS
                if (finishTimeInput) {
                    finishTimeInput.disabled = true;
                    finishTimeInput.value = '';
                }
                if (placementInput) {
                    placementInput.disabled = true;
                    placementInput.value = '';
                }
                if (dnsCheckbox.checked) {
                    dnsCheckbox.checked = false;
                }
            } else {
                // If DNF is unchecked, enable inputs
                if (finishTimeInput) {
                    finishTimeInput.disabled = false;
                }
                if (placementInput) {
                    placementInput.disabled = false;
                }
            }
        }

        // Add event listeners to all DNS checkboxes
        document.querySelectorAll('.dns-checkbox').forEach(function(checkbox) {
            // Initialize state
            handleDnsCheckboxChange(checkbox);

            // Add change event listener
            checkbox.addEventListener('change', function() {
                handleDnsCheckboxChange(this);
            });
        });

        // Add event listeners to all DNF checkboxes
        document.querySelectorAll('.dnf-checkbox').forEach(function(checkbox) {
            // Initialize state
            handleDnfCheckboxChange(checkbox);

            // Add change event listener
            checkbox.addEventListener('change', function() {
                handleDnfCheckboxChange(this);
            });
        });
    });

    // Heat management functionality
    function deleteCurrentHeat() {
        const currentHeatId = {{ .selectedHeat.ID }};
        const eventId = {{ .event.ID }};

        if (confirm('Är du säker på att du vill ta bort denna deltävling?')) {
            fetch(`/heats/${currentHeatId}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (response.ok) {
                    // Redirect back to finish times page
                    window.location.href = `/events/${eventId}/finish-times`;
                } else {
                    return response.json().then(data => {
                        throw new Error(data.error || 'Ett fel uppstod vid borttagning av deltävlingen');
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Fel vid borttagning av deltävling: ' + error.message);
            });
        }
    }
</script>

<!-- Add Heat Modal -->
<div class="modal fade" id="addHeatModal" tabindex="-1" aria-labelledby="addHeatModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="/events/{{.event.ID}}/heats?redirect=finish-times">
                <div class="modal-header">
                    <h5 class="modal-title" id="addHeatModalLabel">Lägg till deltävling</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="heat_number" class="form-label">Nummer</label>
                        <input type="number" class="form-control" id="heat_number" name="heat_number"
                               value="{{add (len .heats) 1}}" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="name" class="form-label">Namn</label>
                        <input type="text" class="form-control" id="name" name="name"
                               value="Heat {{add (len .heats) 1}}" required>
                    </div>
                    {{ if not .event.Entypsegling }}
                    <div class="mb-3">
                        <label for="start_time" class="form-label">Starttid</label>
                        <input type="time" class="form-control" id="start_time" name="start_time"
                               value="{{if .event.Starttid}}{{.event.Starttid}}{{else}}10:00{{end}}" required>
                        <div class="form-text">Starttid för denna deltävling</div>
                    </div>


                    {{ end }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Avbryt</button>
                    <button type="submit" class="btn btn-primary">Lägg till</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Heat Modal (outside Add Heat Modal) -->
<div class="modal fade" id="editHeatModal" tabindex="-1" aria-labelledby="editHeatModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="/heats/{{.selectedHeat.ID}}/details?redirect=finish-times">
                <div class="modal-header">
                    <h5 class="modal-title" id="editHeatModalLabel">Redigera deltävling</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_heat_number" class="form-label">Nummer</label>
                        <input type="number" class="form-control" id="edit_heat_number" name="heat_number"
                               value="{{.selectedHeat.HeatNumber}}" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Namn</label>
                        <input type="text" class="form-control" id="edit_name" name="name"
                               value="{{.selectedHeat.Name}}" required>
                    </div>
                    {{ if not .event.Entypsegling }}
                    <div class="mb-3">
                        <label for="edit_start_time" class="form-label">Starttid</label>
                        <input type="time" class="form-control" id="edit_start_time" name="start_time"
                               value="{{if .selectedHeat.StartTime}}{{.selectedHeat.StartTime}}{{else}}{{.event.Starttid}}{{end}}">
                        <div class="form-text">Ange starttid (HH:MM)</div>
                    </div>
                    {{ end }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Avbryt</button>
                    <button type="submit" class="btn btn-primary">Spara</button>
                </div>
            </form>
        </div>
    </div>
</div>


{{ template "footer.html" . }}
