package database

import (
	"database/sql"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/rbjo<PERSON>gren/segling/pkg/models"
	"github.com/rbjoregren/segling/pkg/utils"
	_ "modernc.org/sqlite" // Import for side effects (driver registration)
)

// DB is a wrapper around sql.DB
type DB struct {
	*sql.DB
}

// New creates a new database connection
func New(dataSourceName string) (*DB, error) {
	// The modernc.org/sqlite driver uses the driver name "sqlite"
	db, err := sql.Open("sqlite", dataSourceName)
	if err != nil {
		return nil, err
	}

	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Check the connection
	if err = db.Ping(); err != nil {
		return nil, err
	}

	// Apply SQLite optimizations
	_, err = db.Exec("PRAGMA journal_mode=WAL")
	if err != nil {
		log.Printf("Warning: Failed to set journal_mode=WAL: %v", err)
	}

	_, err = db.Exec("PRAGMA synchronous=NORMAL")
	if err != nil {
		log.Printf("Warning: Failed to set synchronous=NORMAL: %v", err)
	}

	_, err = db.Exec("PRAGMA cache_size=10000")
	if err != nil {
		log.Printf("Warning: Failed to set cache_size: %v", err)
	}

	_, err = db.Exec("PRAGMA temp_store=MEMORY")
	if err != nil {
		log.Printf("Warning: Failed to set temp_store=MEMORY: %v", err)
	}

	_, err = db.Exec("PRAGMA foreign_keys=ON")
	if err != nil {
		log.Printf("Warning: Failed to enable foreign keys: %v", err)
	}

	_, err = db.Exec("PRAGMA busy_timeout=5000")
	if err != nil {
		log.Printf("Warning: Failed to set busy_timeout: %v", err)
	}

	return &DB{db}, nil
}

// MigrateDatabase performs any necessary database migrations
func (db *DB) MigrateDatabase() error {
	// Check if the event_participants table has the srs_type column
	var count int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'srs_type'
	`).Scan(&count)

	if err != nil {
		return err
	}

	// If the column doesn't exist, add the new columns
	if count == 0 {
		log.Println("Migrating event_participants table to add SRS columns...")

		// SQLite doesn't support adding multiple columns in a single ALTER TABLE statement
		// Add them one by one
		_, err = db.Exec(`ALTER TABLE event_participants ADD COLUMN srs_type TEXT DEFAULT 'srs';`)
		if err != nil {
			return err
		}

		_, err = db.Exec(`ALTER TABLE event_participants ADD COLUMN selected_srs_value REAL DEFAULT 0;`)
		if err != nil {
			return err
		}

		_, err = db.Exec(`ALTER TABLE event_participants ADD COLUMN custom_srs_value REAL DEFAULT 0;`)
		if err != nil {
			return err
		}

		_, err = db.Exec(`ALTER TABLE event_participants ADD COLUMN use_custom_srs_value BOOLEAN DEFAULT 0;`)
		if err != nil {
			return err
		}

		// Update the selected_srs_value for existing participants
		log.Println("Updating SRS values for existing participants...")
		rows, err := db.Query(`
			SELECT ep.id, b.srs
			FROM event_participants ep
			JOIN boats b ON ep.boat_id = b.id
		`)
		if err != nil {
			log.Printf("Warning: Could not update SRS values for existing participants: %v", err)
		} else {
			defer rows.Close()

			for rows.Next() {
				var id int64
				var srs float64
				if err := rows.Scan(&id, &srs); err != nil {
					log.Printf("Warning: Error scanning row: %v", err)
					continue
				}

				_, err = db.Exec(`
					UPDATE event_participants
					SET selected_srs_value = ?
					WHERE id = ?
				`, srs, id)
				if err != nil {
					log.Printf("Warning: Could not update SRS value for participant %d: %v", id, err)
				}
			}
		}

		log.Println("Migration completed successfully")
	}

	// Check if the events table has the starttid column
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'starttid'
	`).Scan(&count)

	if err != nil {
		return err
	}

	// If the column doesn't exist, add the new columns
	if count == 0 {
		log.Println("Migrating events table to add Starttid, Vind, and Banlangd columns...")

		// SQLite doesn't support adding multiple columns in a single ALTER TABLE statement
		// Add them one by one
		_, err = db.Exec(`ALTER TABLE events ADD COLUMN starttid TEXT DEFAULT '';`)
		if err != nil {
			return err
		}

		_, err = db.Exec(`ALTER TABLE events ADD COLUMN vind INTEGER DEFAULT 0;`)
		if err != nil {
			return err
		}

		_, err = db.Exec(`ALTER TABLE events ADD COLUMN banlangd INTEGER DEFAULT 0;`)
		if err != nil {
			return err
		}

		log.Println("Events table migration completed successfully")
	}

	// Check if the events table has the jaktstart column
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart'
	`).Scan(&count)

	if err != nil {
		return err
	}

	// If the column doesn't exist, add it
	if count == 0 {
		log.Println("Migrating events table to add Jaktstart column...")

		_, err = db.Exec(`ALTER TABLE events ADD COLUMN jaktstart BOOLEAN DEFAULT 0;`)
		if err != nil {
			return err
		}

		log.Println("Jaktstart column added successfully")
	}

	// Check if the event_participants table has the finish_time column
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'finish_time'
	`).Scan(&count)

	if err != nil {
		return err
	}

	// If the column doesn't exist, add it
	if count == 0 {
		log.Println("Migrating event_participants table to add finish_time column...")

		_, err = db.Exec(`ALTER TABLE event_participants ADD COLUMN finish_time TEXT DEFAULT '';`)
		if err != nil {
			return err
		}

		log.Println("finish_time column added successfully")
	}

	// Check if the event_participants table has the crew_count column
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'crew_count'
	`).Scan(&count)

	if err != nil {
		return err
	}

	// If the column doesn't exist, add it
	if count == 0 {
		log.Println("Migrating event_participants table to add crew_count column...")

		_, err = db.Exec(`ALTER TABLE event_participants ADD COLUMN crew_count INTEGER DEFAULT 0;`)
		if err != nil {
			return err
		}

		log.Println("crew_count column added successfully")
	}

	// Check if the events table has the locked column
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'locked'
	`).Scan(&count)

	if err != nil {
		return err
	}

	// If the column doesn't exist, add it
	if count == 0 {
		log.Println("Migrating events table to add locked column...")

		_, err = db.Exec(`ALTER TABLE events ADD COLUMN locked BOOLEAN DEFAULT 0;`)
		if err != nil {
			return err
		}

		log.Println("locked column added successfully")
	}

	// Check if the events table has the tavlingstyp column
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'tavlingstyp'
	`).Scan(&count)

	if err != nil {
		return err
	}

	// If the column doesn't exist, add it
	if count == 0 {
		log.Println("Migrating events table to add tavlingstyp column...")

		_, err = db.Exec(`ALTER TABLE events ADD COLUMN tavlingstyp TEXT DEFAULT '';`)
		if err != nil {
			return err
		}

		log.Println("tavlingstyp column added successfully")
	}

	// Check if the saved_results table exists
	err = db.QueryRow(`
		SELECT COUNT(*) FROM sqlite_master
		WHERE type='table' AND name='saved_results'
	`).Scan(&count)

	if err != nil {
		return err
	}

	// If the table doesn't exist, create it
	if count == 0 {
		log.Println("Creating saved_results table...")

		_, err = db.Exec(`
			CREATE TABLE saved_results (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				event_id INTEGER NOT NULL,
				position INTEGER NOT NULL,
				sailor_id INTEGER NOT NULL,
				sailor_name TEXT NOT NULL,
				sailor_club TEXT NOT NULL,
				boat_id INTEGER NOT NULL,
				boat_name TEXT NOT NULL,
				boat_type TEXT NOT NULL,
				matbrevs_nummer TEXT,
				segelnummer TEXT,
				nationality TEXT,
				srs_value REAL NOT NULL,
				srs_type TEXT NOT NULL,
				use_custom_srs_value BOOLEAN DEFAULT 0,
				crew_count INTEGER NOT NULL,
				start_time TEXT NOT NULL,
				finish_time TEXT NOT NULL,
				elapsed_time TEXT NOT NULL,
				corrected_time TEXT NOT NULL,
				time_to_previous TEXT NOT NULL,
				time_to_winner TEXT NOT NULL,
				elapsed_seconds INTEGER DEFAULT 0,
				corrected_seconds INTEGER NOT NULL,
				corrected_seconds_float REAL NOT NULL,
				dns BOOLEAN DEFAULT 0,
				dnf BOOLEAN DEFAULT 0,
				created_at DATETIME NOT NULL,
				FOREIGN KEY (event_id) REFERENCES events (id) ON DELETE CASCADE
			)
		`)
		if err != nil {
			return err
		}

		log.Println("saved_results table created successfully")
	} else {
		// Check if the use_custom_srs_value column exists
		err = db.QueryRow(`
			SELECT COUNT(*) FROM pragma_table_info('saved_results')
			WHERE name = 'use_custom_srs_value'
		`).Scan(&count)

		if err != nil {
			return err
		}

		// If the column doesn't exist, add it
		if count == 0 {
			log.Println("Migrating saved_results table to add use_custom_srs_value column...")

			_, err = db.Exec(`ALTER TABLE saved_results ADD COLUMN use_custom_srs_value BOOLEAN DEFAULT 0;`)
			if err != nil {
				return err
			}

			log.Println("use_custom_srs_value column added successfully to saved_results table")
		}

		// Check if the corrected_seconds_float column exists
		err = db.QueryRow(`
			SELECT COUNT(*) FROM pragma_table_info('saved_results')
			WHERE name = 'corrected_seconds_float'
		`).Scan(&count)

		if err != nil {
			return err
		}

		// If the column doesn't exist, add it
		if count == 0 {
			log.Println("Migrating saved_results table to add corrected_seconds_float column...")

			_, err = db.Exec(`ALTER TABLE saved_results ADD COLUMN corrected_seconds_float REAL DEFAULT 0;`)
			if err != nil {
				return err
			}

			log.Println("corrected_seconds_float column added successfully to saved_results table")
		}

		// Check if the elapsed_seconds column exists
		err = db.QueryRow(`
			SELECT COUNT(*) FROM pragma_table_info('saved_results')
			WHERE name = 'elapsed_seconds'
		`).Scan(&count)

		if err != nil {
			return err
		}

		// If the column doesn't exist, add it
		if count == 0 {
			log.Println("Migrating saved_results table to add elapsed_seconds column...")

			_, err = db.Exec(`ALTER TABLE saved_results ADD COLUMN elapsed_seconds INTEGER DEFAULT 0;`)
			if err != nil {
				return err
			}

			log.Println("elapsed_seconds column added successfully to saved_results table")
		}

		// Check if the dns column exists
		err = db.QueryRow(`
			SELECT COUNT(*) FROM pragma_table_info('saved_results')
			WHERE name = 'dns'
		`).Scan(&count)

		if err != nil {
			return err
		}

		// If the column doesn't exist, add it
		if count == 0 {
			log.Println("Migrating saved_results table to add dns column...")

			_, err = db.Exec(`ALTER TABLE saved_results ADD COLUMN dns BOOLEAN DEFAULT 0;`)
			if err != nil {
				return err
			}

			log.Println("dns column added successfully to saved_results table")
		}

		// Check if the dnf column exists
		err = db.QueryRow(`
			SELECT COUNT(*) FROM pragma_table_info('saved_results')
			WHERE name = 'dnf'
		`).Scan(&count)

		if err != nil {
			return err
		}

		// If the column doesn't exist, add it
		if count == 0 {
			log.Println("Migrating saved_results table to add dnf column...")

			_, err = db.Exec(`ALTER TABLE saved_results ADD COLUMN dnf BOOLEAN DEFAULT 0;`)
			if err != nil {
				return err
			}

			log.Println("dnf column added successfully to saved_results table")
		}
	}

	// Check if the heats table exists
	err = db.QueryRow(`
		SELECT COUNT(*) FROM sqlite_master
		WHERE type='table' AND name='heats'
	`).Scan(&count)
	if err != nil {
		return err
	}

	// If the table doesn't exist, create it
	if count == 0 {
		log.Println("Creating heats table...")

		_, err = db.Exec(`
			CREATE TABLE heats (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				event_id INTEGER NOT NULL,
				heat_number INTEGER NOT NULL,
				name TEXT,
				start_time TEXT DEFAULT '',
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
				FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
			)
		`)
		if err != nil {
			return err
		}

		log.Println("heats table created successfully")
	} else {
		// Check if the start_time column exists
		err = db.QueryRow(`
			SELECT COUNT(*) FROM pragma_table_info('heats')
			WHERE name = 'start_time'
		`).Scan(&count)
		if err != nil {
			return err
		}

		// If the column doesn't exist, add it
		if count == 0 {
			log.Println("Adding start_time column to heats table...")

			_, err = db.Exec(`ALTER TABLE heats ADD COLUMN start_time TEXT DEFAULT '';`)
			if err != nil {
				return err
			}

			log.Println("start_time column added to heats table successfully")
		}
	}

	// Check if the heat_finish_times table exists
	err = db.QueryRow(`
		SELECT COUNT(*) FROM sqlite_master
		WHERE type='table' AND name='heat_finish_times'
	`).Scan(&count)
	if err != nil {
		return err
	}

	// If the table doesn't exist, create it
	if count == 0 {
		log.Println("Creating heat_finish_times table...")

		_, err = db.Exec(`
			CREATE TABLE heat_finish_times (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				heat_id INTEGER NOT NULL,
				participant_id INTEGER NOT NULL,
				finish_time TEXT DEFAULT '',
				dns BOOLEAN DEFAULT 0,
				dnf BOOLEAN DEFAULT 0,
				created_at DATETIME NOT NULL,
				updated_at DATETIME NOT NULL,
				FOREIGN KEY (heat_id) REFERENCES heats(id) ON DELETE CASCADE,
				FOREIGN KEY (participant_id) REFERENCES event_participants(id) ON DELETE CASCADE,
				UNIQUE(heat_id, participant_id)
			)
		`)
		if err != nil {
			return err
		}

		log.Println("heat_finish_times table created successfully")
	}

	return nil
}

// Initialize creates the database tables if they don't exist
func (db *DB) Initialize() error {
	// Ensure credentials directory exists
	if err := utils.EnsureCredentialsDirExists(); err != nil {
		log.Printf("Warning: Failed to ensure credentials directory exists: %v", err)
	}

	// Migrate GitHub token from database to file if needed
	if err := db.migrateGitHubToken(); err != nil {
		log.Printf("Warning: Failed to migrate GitHub token: %v", err)
	}
	// Create sailors table
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS sailors (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			namn TEXT NOT NULL,
			telefon TEXT NOT NULL,
			klubb TEXT DEFAULT 'LSS',
			created_at DATETIME NOT NULL,
			updated_at DATETIME NOT NULL
		)
	`)
	if err != nil {
		return err
	}

	// Create boats table
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS boats (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			namn TEXT NOT NULL,
			battyp TEXT NOT NULL,
			matbrevs_nummer TEXT,
			segelnummer TEXT,
			nationality TEXT DEFAULT 'SWE',
			srs REAL NOT NULL,
			srs_utan_undanvindsegel REAL NOT NULL,
			srs_shorthanded REAL,
			srs_shorthanded_utan_undanvindsegel REAL,
			created_at DATETIME NOT NULL,
			updated_at DATETIME NOT NULL
		)
	`)
	if err != nil {
		return err
	}

	// Create events table
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS events (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			namn TEXT NOT NULL,
			datum DATETIME NOT NULL,
			beskrivning TEXT,
			created_at DATETIME NOT NULL,
			updated_at DATETIME NOT NULL
		)
	`)
	if err != nil {
		return err
	}

	// Create event_participants table
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS event_participants (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			event_id INTEGER NOT NULL,
			sailor_id INTEGER NOT NULL,
			boat_id INTEGER NOT NULL,
			srs_type TEXT,
			selected_srs_value REAL,
			custom_srs_value REAL,
			use_custom_srs_value BOOLEAN DEFAULT 0,
			finish_time TEXT DEFAULT '',
			created_at DATETIME NOT NULL,
			updated_at DATETIME NOT NULL,
			FOREIGN KEY (event_id) REFERENCES events (id) ON DELETE CASCADE,
			FOREIGN KEY (sailor_id) REFERENCES sailors (id) ON DELETE CASCADE,
			FOREIGN KEY (boat_id) REFERENCES boats (id) ON DELETE CASCADE
		)
	`)
	if err != nil {
		return err
	}

	// Create srs_boat_types table for storing data from SRS table
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS srs_boat_types (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			battyp TEXT NOT NULL UNIQUE,
			srs REAL NOT NULL,
			srs_utan_undanvindsegel REAL NOT NULL,
			srs_shorthanded REAL,
			srs_shorthanded_utan_undanvindsegel REAL,
			created_at DATETIME NOT NULL,
			updated_at DATETIME NOT NULL
		)
	`)
	if err != nil {
		return err
	}

	// Create srs_matbrev table for storing data from ApprovedList
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS srs_matbrev (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			matbrevs_nummer TEXT NOT NULL UNIQUE,
			battyp TEXT NOT NULL,
			bat_namn TEXT,
			agare TEXT,
			segelnummer TEXT,
			nationality TEXT DEFAULT 'SWE',
			srs REAL NOT NULL,
			srs_utan_undanvindsegel REAL NOT NULL,
			srs_shorthanded REAL,
			srs_shorthanded_utan_undanvindsegel REAL,
			created_at DATETIME NOT NULL,
			updated_at DATETIME NOT NULL
		)
	`)
	if err != nil {
		return err
	}

	// Create srs_sync_log table to track when data was last synced
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS srs_sync_log (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			source TEXT NOT NULL,
			status TEXT NOT NULL,
			message TEXT,
			created_at DATETIME NOT NULL
		)
	`)
	if err != nil {
		return err
	}

	// Create settings table for application settings
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS settings (
			key TEXT PRIMARY KEY,
			value TEXT NOT NULL,
			created_at DATETIME NOT NULL,
			updated_at DATETIME NOT NULL
		)
	`)
	if err != nil {
		return err
	}

	// Initialize default settings if they don't exist
	err = db.initializeDefaultSettings()
	if err != nil {
		return err
	}

	log.Println("Database initialized successfully")

	// Run migrations
	if err := db.MigrateDatabase(); err != nil {
		return err
	}

	// Ensure entypsegling columns exist
	if err := db.ensureEntypSeglingColumns(); err != nil {
		return err
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// GetSailors returns all sailors from the database
func (db *DB) GetSailors() ([]models.Sailor, error) {
	rows, err := db.Query(`
		SELECT id, namn, telefon, klubb, created_at, updated_at
		FROM sailors
		ORDER BY namn
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var sailors []models.Sailor
	for rows.Next() {
		var s models.Sailor
		err := rows.Scan(&s.ID, &s.Namn, &s.Telefon, &s.Klubb, &s.CreatedAt, &s.UpdatedAt)
		if err != nil {
			return nil, err
		}
		sailors = append(sailors, s)
	}

	return sailors, nil
}

// GetSailor returns a single sailor by ID
func (db *DB) GetSailor(id int64) (models.Sailor, error) {
	var s models.Sailor
	err := db.QueryRow(`
		SELECT id, namn, telefon, klubb, created_at, updated_at
		FROM sailors
		WHERE id = ?
	`, id).Scan(&s.ID, &s.Namn, &s.Telefon, &s.Klubb, &s.CreatedAt, &s.UpdatedAt)

	return s, err
}

// CreateSailor adds a new sailor to the database
func (db *DB) CreateSailor(namn, telefon, klubb string) (int64, error) {
	now := time.Now()

	// Default klubb to the default_club setting if empty
	if klubb == "" {
		defaultClub, err := db.GetSetting("default_club")
		if err != nil || defaultClub == "" {
			defaultClub = "LSS" // Fallback to LSS if setting not found
		}
		klubb = defaultClub
	}

	result, err := db.Exec(`
		INSERT INTO sailors (namn, telefon, klubb, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?)
	`, namn, telefon, klubb, now, now)
	if err != nil {
		return 0, err
	}

	return result.LastInsertId()
}

// UpdateSailor updates an existing sailor
func (db *DB) UpdateSailor(id int64, namn, telefon, klubb string) error {
	now := time.Now()

	// Default klubb to the default_club setting if empty
	if klubb == "" {
		defaultClub, err := db.GetSetting("default_club")
		if err != nil || defaultClub == "" {
			defaultClub = "LSS" // Fallback to LSS if setting not found
		}
		klubb = defaultClub
	}

	_, err := db.Exec(`
		UPDATE sailors
		SET namn = ?, telefon = ?, klubb = ?, updated_at = ?
		WHERE id = ?
	`, namn, telefon, klubb, now, id)

	return err
}

// DeleteSailor removes a sailor from the database
func (db *DB) DeleteSailor(id int64) error {
	_, err := db.Exec(`DELETE FROM sailors WHERE id = ?`, id)
	return err
}

// SearchSailors searches for sailors by name, phone number, or club
func (db *DB) SearchSailors(query string) ([]models.Sailor, error) {
	searchQuery := "%" + query + "%"
	rows, err := db.Query(`
		SELECT id, namn, telefon, klubb, created_at, updated_at
		FROM sailors
		WHERE namn LIKE ? OR telefon LIKE ? OR klubb LIKE ?
		ORDER BY namn
	`, searchQuery, searchQuery, searchQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var sailors []models.Sailor
	for rows.Next() {
		var s models.Sailor
		err := rows.Scan(&s.ID, &s.Namn, &s.Telefon, &s.Klubb, &s.CreatedAt, &s.UpdatedAt)
		if err != nil {
			return nil, err
		}
		sailors = append(sailors, s)
	}

	return sailors, nil
}

// GetBoats returns all boats from the database
func (db *DB) GetBoats() ([]models.Boat, error) {
	rows, err := db.Query(`
		SELECT id, namn, battyp, matbrevs_nummer, segelnummer, nationality, srs, srs_utan_undanvindsegel,
		       srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM boats
		ORDER BY namn
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var boats []models.Boat
	for rows.Next() {
		var b models.Boat
		err := rows.Scan(&b.ID, &b.Namn, &b.Battyp, &b.MatbrevsNummer, &b.Segelnummer, &b.Nationality, &b.SRS, &b.SRSUtanUndanvindsegel,
			&b.SRSShorthanded, &b.SRSShorthandedUtanUndanvindsegel, &b.CreatedAt, &b.UpdatedAt)
		if err != nil {
			return nil, err
		}
		boats = append(boats, b)
	}

	return boats, nil
}

// GetBoat returns a single boat by ID
func (db *DB) GetBoat(id int64) (models.Boat, error) {
	var b models.Boat
	err := db.QueryRow(`
		SELECT id, namn, battyp, matbrevs_nummer, segelnummer, nationality, srs, srs_utan_undanvindsegel,
		       srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM boats
		WHERE id = ?
	`, id).Scan(&b.ID, &b.Namn, &b.Battyp, &b.MatbrevsNummer, &b.Segelnummer, &b.Nationality, &b.SRS, &b.SRSUtanUndanvindsegel,
		&b.SRSShorthanded, &b.SRSShorthandedUtanUndanvindsegel, &b.CreatedAt, &b.UpdatedAt)

	return b, err
}

// CreateBoat adds a new boat to the database
func (db *DB) CreateBoat(namn, battyp, matbrevsNummer, segelnummer, nationality string, srs, srsUtanUndanvindsegel, srsShorthanded, srsShorthandedUtanUndanvindsegel float64) (int64, error) {
	now := time.Now()

	// Default nationality to SWE if empty
	if nationality == "" {
		nationality = "SWE"
	}

	result, err := db.Exec(`
		INSERT INTO boats (namn, battyp, matbrevs_nummer, segelnummer, nationality, srs, srs_utan_undanvindsegel,
		                   srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`, namn, battyp, matbrevsNummer, segelnummer, nationality, srs, srsUtanUndanvindsegel, srsShorthanded, srsShorthandedUtanUndanvindsegel, now, now)
	if err != nil {
		return 0, err
	}

	return result.LastInsertId()
}

// UpdateBoat updates an existing boat
func (db *DB) UpdateBoat(id int64, namn, battyp, matbrevsNummer, segelnummer, nationality string, srs, srsUtanUndanvindsegel, srsShorthanded, srsShorthandedUtanUndanvindsegel float64) error {
	now := time.Now()

	// Default nationality to SWE if empty
	if nationality == "" {
		nationality = "SWE"
	}

	_, err := db.Exec(`
		UPDATE boats
		SET namn = ?, battyp = ?, matbrevs_nummer = ?, segelnummer = ?, nationality = ?, srs = ?, srs_utan_undanvindsegel = ?,
		    srs_shorthanded = ?, srs_shorthanded_utan_undanvindsegel = ?, updated_at = ?
		WHERE id = ?
	`, namn, battyp, matbrevsNummer, segelnummer, nationality, srs, srsUtanUndanvindsegel, srsShorthanded, srsShorthandedUtanUndanvindsegel, now, id)

	return err
}

// DeleteBoat removes a boat from the database
func (db *DB) DeleteBoat(id int64) error {
	_, err := db.Exec(`DELETE FROM boats WHERE id = ?`, id)
	return err
}

// SearchBoats searches for boats by name, boat type, or mätbrevs nummer
func (db *DB) SearchBoats(query string) ([]models.Boat, error) {
	// Special case for "mätbrevs nummer" search
	if query == "mätbrevs nummer" {
		// First, get all boats from the database
		allBoats, err := db.GetBoats()
		if err != nil {
			return nil, err
		}

		// Filter boats that have a mätbrevs nummer
		var filteredBoats []models.Boat
		for _, boat := range allBoats {
			if boat.MatbrevsNummer != "" {
				filteredBoats = append(filteredBoats, boat)
			}
		}

		// Also try to find boats by owner in the SRS data
		// This is done as a best effort and won't return an error if it fails
		ownerResults, err := utils.SearchBoatsByOwner("")
		if err == nil && len(ownerResults) > 0 {
			// For each boat found by owner, check if we already have it in our filtered list
			for _, srsData := range ownerResults {
				found := false
				for _, boat := range filteredBoats {
					if boat.MatbrevsNummer == srsData.MatbrevsNummer {
						found = true
						break
					}
				}

				// If we don't have this boat in our database yet, create a new one
				if !found && srsData.MatbrevsNummer != "" {
					// Create a new boat with the data from SRS
					now := time.Now()
					boat := models.Boat{
						Namn:                             srsData.Battyp + " (" + srsData.Agare + ")",
						Battyp:                           srsData.Battyp,
						MatbrevsNummer:                   srsData.MatbrevsNummer,
						SRS:                              srsData.SRS,
						SRSUtanUndanvindsegel:            srsData.SRSUtanUndanvindsegel,
						SRSShorthanded:                   srsData.SRSShorthanded,
						SRSShorthandedUtanUndanvindsegel: srsData.SRSShorthandedUtanUndanvindsegel,
						CreatedAt:                        now,
						UpdatedAt:                        now,
					}

					// Add it to our filtered list
					filteredBoats = append(filteredBoats, boat)
				}
			}
		}

		return filteredBoats, nil
	}

	// Normal search for other queries
	searchQuery := "%" + query + "%"
	rows, err := db.Query(`
		SELECT id, namn, battyp, matbrevs_nummer, srs, srs_utan_undanvindsegel,
		       srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM boats
		WHERE namn LIKE ? OR battyp LIKE ? OR matbrevs_nummer LIKE ?
		ORDER BY namn
	`, searchQuery, searchQuery, searchQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var boats []models.Boat
	for rows.Next() {
		var b models.Boat
		err := rows.Scan(&b.ID, &b.Namn, &b.Battyp, &b.MatbrevsNummer, &b.SRS, &b.SRSUtanUndanvindsegel,
			&b.SRSShorthanded, &b.SRSShorthandedUtanUndanvindsegel, &b.CreatedAt, &b.UpdatedAt)
		if err != nil {
			return nil, err
		}
		boats = append(boats, b)
	}

	return boats, nil
}

// CountEventParticipants returns the total number of people (skipper + crew) for an event
func (db *DB) CountEventParticipants(eventID int64) (int, error) {
	// Check if the event_participants table has the crew_count column
	var hasCrewCount int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'crew_count'
	`).Scan(&hasCrewCount)

	if err != nil {
		log.Printf("ERROR: CountEventParticipants - Failed to check crew_count column: %v", err)
		return 0, err
	}

	var totalPeople int
	if hasCrewCount > 0 {
		// Sum up crew_count + 1 (skipper) for each participant
		err = db.QueryRow(`
			SELECT COALESCE(SUM(COALESCE(crew_count, 0) + 1), 0)
			FROM event_participants
			WHERE event_id = ?
		`, eventID).Scan(&totalPeople)
	} else {
		// If no crew_count column, just count participants (1 person per participant)
		err = db.QueryRow(`
			SELECT COUNT(*)
			FROM event_participants
			WHERE event_id = ?
		`, eventID).Scan(&totalPeople)
	}

	if err != nil {
		log.Printf("ERROR: CountEventParticipants - Failed to count people for event %d: %v", eventID, err)
		return 0, err
	}

	log.Printf("DEBUG: CountEventParticipants - Event %d has %d total people", eventID, totalPeople)
	return totalPeople, nil
}

// CountEventBoats returns the number of boats (participant records) for an event
func (db *DB) CountEventBoats(eventID int64) (int, error) {
	var count int
	err := db.QueryRow(`
		SELECT COUNT(*)
		FROM event_participants
		WHERE event_id = ?
	`, eventID).Scan(&count)

	if err != nil {
		log.Printf("ERROR: CountEventBoats - Failed to count boats for event %d: %v", eventID, err)
		return 0, err
	}

	log.Printf("DEBUG: CountEventBoats - Event %d has %d boats", eventID, count)
	return count, nil
}

// GetEvents returns all events from the database
func (db *DB) GetEvents() ([]models.Event, error) {
	return db.GetEventsByYear(0) // 0 means all years
}

// GetEventsByYear returns events from the database filtered by year
// If year is 0, returns all events
func (db *DB) GetEventsByYear(year int) ([]models.Event, error) {
	return db.GetEventsByYearAndType(year, "")
}

// GetEventsByYearAndType returns events from the database filtered by year and competition type
// If year is 0, returns events from all years
// If competitionType is empty, returns events of all types
func (db *DB) GetEventsByYearAndType(year int, competitionType string) ([]models.Event, error) {
	// Check if the events table has the starttid column
	var count int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'starttid'
	`).Scan(&count)

	if err != nil {
		return nil, err
	}

	var rows *sql.Rows

	// Check if the events table has the jaktstart column
	var hasJaktstart int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart'
	`).Scan(&hasJaktstart)

	if err != nil {
		return nil, err
	}

	// Check if the events table has the tavlingstyp column
	var hasTavlingstyp int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'tavlingstyp'
	`).Scan(&hasTavlingstyp)

	if err != nil {
		return nil, err
	}

	// Check if the events table has the entypsegling column
	var hasEntypsegling int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'entypsegling'
	`).Scan(&hasEntypsegling)

	if err != nil {
		return nil, err
	}

	// Check if the events table has the boat_type column
	var hasBoatType int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'boat_type'
	`).Scan(&hasBoatType)

	if err != nil {
		return nil, err
	}

	// Check if the events table has the jaktstart_type column
	var hasJaktstartType int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart_type'
	`).Scan(&hasJaktstartType)

	if err != nil {
		return nil, err
	}

	// Build the query based on schema and year filter
	var query string
	var args []interface{}

	// Base query depends on schema
	if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 && hasEntypsegling > 0 && hasBoatType > 0 && hasJaktstartType > 0 {
		query = `
			SELECT id, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boat_type, jaktstart_type, beskrivning, locked, created_at, updated_at
			FROM events
		`
	} else if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 && hasEntypsegling > 0 && hasBoatType > 0 {
		query = `
			SELECT id, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boat_type, beskrivning, locked, created_at, updated_at
			FROM events
		`
	} else if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 {
		query = `
			SELECT id, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, beskrivning, locked, created_at, updated_at
			FROM events
		`
	} else if count > 0 && hasJaktstart > 0 {
		query = `
			SELECT id, namn, datum, starttid, vind, banlangd, jaktstart, beskrivning, locked, created_at, updated_at
			FROM events
		`
	} else if count > 0 {
		query = `
			SELECT id, namn, datum, starttid, vind, banlangd, beskrivning, locked, created_at, updated_at
			FROM events
		`
	} else {
		query = `
			SELECT id, namn, datum, beskrivning, locked, created_at, updated_at
			FROM events
		`
	}

	// Add filters if specified
	var whereClause []string

	// Year filter
	if year > 0 {
		// Use date range to filter by year instead of strftime
		startDate := fmt.Sprintf("%04d-01-01", year)
		endDate := fmt.Sprintf("%04d-12-31", year)
		whereClause = append(whereClause, "datum >= ? AND datum <= ?")
		args = append(args, startDate, endDate)
	}

	// Competition type filter
	if competitionType != "" && hasTavlingstyp > 0 {
		whereClause = append(whereClause, "tavlingstyp = ?")
		args = append(args, competitionType)
	}

	// Add WHERE clause if we have any filters
	if len(whereClause) > 0 {
		query += " WHERE " + strings.Join(whereClause, " AND ")
	}

	// Add order by
	query += ` ORDER BY datum DESC`

	// Execute the query
	rows, err = db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var events []models.Event
	for rows.Next() {
		var e models.Event
		var beskrivning sql.NullString // Use sql.NullString to handle NULL values

		// If all columns exist, use the newest schema with jaktstart_type support
		if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 && hasEntypsegling > 0 && hasBoatType > 0 && hasJaktstartType > 0 {
			err := rows.Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &e.Jaktstart, &e.Tavlingstyp, &e.Entypsegling, &e.BoatType, &e.JaktstartType, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
			if err != nil {
				return nil, err
			}
		} else if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 && hasEntypsegling > 0 && hasBoatType > 0 {
			err := rows.Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &e.Jaktstart, &e.Tavlingstyp, &e.Entypsegling, &e.BoatType, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
			if err != nil {
				return nil, err
			}
			// Set JaktstartType based on legacy fields for backward compatibility
			if e.Entypsegling {
				e.JaktstartType = "entypsegling"
			} else if e.Jaktstart {
				e.JaktstartType = "regular"
			} else {
				e.JaktstartType = "none"
			}
		} else if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 {
			err := rows.Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &e.Jaktstart, &e.Tavlingstyp, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
			if err != nil {
				return nil, err
			}
			// Set JaktstartType based on legacy fields for backward compatibility
			if e.Jaktstart {
				e.JaktstartType = "regular"
			} else {
				e.JaktstartType = "none"
			}
		} else if count > 0 && hasJaktstart > 0 {
			err := rows.Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &e.Jaktstart, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
			if err != nil {
				return nil, err
			}
			// Set JaktstartType based on legacy fields for backward compatibility
			if e.Jaktstart {
				e.JaktstartType = "regular"
			} else {
				e.JaktstartType = "none"
			}
		} else if count > 0 {
			// If only starttid exists but not jaktstart, use the intermediate schema
			err := rows.Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
			if err != nil {
				return nil, err
			}
			e.JaktstartType = "none"
		} else {
			// Otherwise, scan only the old fields
			err := rows.Scan(&e.ID, &e.Namn, &e.Datum, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
			if err != nil {
				return nil, err
			}
			e.JaktstartType = "none"
		}

		// Set the beskrivning field to an empty string if it's NULL
		if beskrivning.Valid {
			e.Beskrivning = beskrivning.String
		} else {
			e.Beskrivning = ""
		}

		// Get participant count for this event
		participantCount, err := db.CountEventParticipants(e.ID)
		if err != nil {
			// If there's an error, just set the count to 0 and continue
			participantCount = 0
		}

		// Store the participant count in the event's ParticipantCount field
		e.ParticipantCount = participantCount

		events = append(events, e)
	}

	return events, nil
}

// GetEvent returns a single event by ID
func (db *DB) GetEvent(id int64) (models.Event, error) {
	var e models.Event
	var beskrivning sql.NullString // Use sql.NullString to handle NULL values

	// Check if the events table has the starttid column
	var count int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'starttid'
	`).Scan(&count)

	if err != nil {
		return e, err
	}

	// Check if the events table has the jaktstart column
	var hasJaktstart int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart'
	`).Scan(&hasJaktstart)

	if err != nil {
		return e, err
	}

	// Check if the events table has the tavlingstyp column
	var hasTavlingstyp int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'tavlingstyp'
	`).Scan(&hasTavlingstyp)

	if err != nil {
		return e, err
	}

	// Check if the events table has the entypsegling column
	var hasEntypsegling int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'entypsegling'
	`).Scan(&hasEntypsegling)

	if err != nil {
		return e, err
	}

	// Check if the events table has the boat_type column
	var hasBoatType int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'boat_type'
	`).Scan(&hasBoatType)

	if err != nil {
		return e, err
	}

	// Check if the events table has the jaktstart_type column
	var hasJaktstartType int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart_type'
	`).Scan(&hasJaktstartType)

	if err != nil {
		return e, err
	}

	// Check if the events table has the discard_after_heats column
	var hasDiscardAfterHeats int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'discard_after_heats'
	`).Scan(&hasDiscardAfterHeats)

	if err != nil {
		return e, err
	}

	// If all columns exist, use the newest schema with discard_after_heats support
	if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 && hasEntypsegling > 0 && hasBoatType > 0 && hasJaktstartType > 0 && hasDiscardAfterHeats > 0 {
		err = db.QueryRow(`
			SELECT id, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boat_type, jaktstart_type, discard_after_heats, beskrivning, locked, created_at, updated_at
			FROM events
			WHERE id = ?
		`, id).Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &e.Jaktstart, &e.Tavlingstyp, &e.Entypsegling, &e.BoatType, &e.JaktstartType, &e.DiscardAfterHeats, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
	} else if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 && hasEntypsegling > 0 && hasBoatType > 0 && hasJaktstartType > 0 {
		err = db.QueryRow(`
			SELECT id, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boat_type, jaktstart_type, beskrivning, locked, created_at, updated_at
			FROM events
			WHERE id = ?
		`, id).Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &e.Jaktstart, &e.Tavlingstyp, &e.Entypsegling, &e.BoatType, &e.JaktstartType, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
		// Set default value for discard_after_heats
		e.DiscardAfterHeats = 0
	} else if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 && hasEntypsegling > 0 && hasBoatType > 0 {
		err = db.QueryRow(`
			SELECT id, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boat_type, beskrivning, locked, created_at, updated_at
			FROM events
			WHERE id = ?
		`, id).Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &e.Jaktstart, &e.Tavlingstyp, &e.Entypsegling, &e.BoatType, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
		// Set JaktstartType based on legacy fields for backward compatibility
		if e.Entypsegling {
			e.JaktstartType = "entypsegling"
		} else if e.Jaktstart {
			e.JaktstartType = "regular"
		} else {
			e.JaktstartType = "none"
		}
		// Set default value for discard_after_heats
		e.DiscardAfterHeats = 0
	} else if count > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 {
		err = db.QueryRow(`
			SELECT id, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, beskrivning, locked, created_at, updated_at
			FROM events
			WHERE id = ?
		`, id).Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &e.Jaktstart, &e.Tavlingstyp, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
		// Set JaktstartType based on legacy fields for backward compatibility
		if e.Jaktstart {
			e.JaktstartType = "regular"
		} else {
			e.JaktstartType = "none"
		}
		// Set default value for discard_after_heats
		e.DiscardAfterHeats = 0
	} else if count > 0 && hasJaktstart > 0 {
		err = db.QueryRow(`
			SELECT id, namn, datum, starttid, vind, banlangd, jaktstart, beskrivning, locked, created_at, updated_at
			FROM events
			WHERE id = ?
		`, id).Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &e.Jaktstart, &beskrivning, &e.Locked, &e.CreatedAt, &e.UpdatedAt)
		// Set JaktstartType based on legacy fields for backward compatibility
		if e.Jaktstart {
			e.JaktstartType = "regular"
		} else {
			e.JaktstartType = "none"
		}
		// Set default value for discard_after_heats
		e.DiscardAfterHeats = 0
	} else if count > 0 {
		// If only starttid exists but not jaktstart, use the intermediate schema
		err = db.QueryRow(`
			SELECT id, namn, datum, starttid, vind, banlangd, beskrivning, created_at, updated_at
			FROM events
			WHERE id = ?
		`, id).Scan(&e.ID, &e.Namn, &e.Datum, &e.Starttid, &e.Vind, &e.Banlangd, &beskrivning, &e.CreatedAt, &e.UpdatedAt)
		e.JaktstartType = "none"
		// Set default value for discard_after_heats
		e.DiscardAfterHeats = 0
	} else {
		// Otherwise, use the old schema
		err = db.QueryRow(`
			SELECT id, namn, datum, beskrivning, created_at, updated_at
			FROM events
			WHERE id = ?
		`, id).Scan(&e.ID, &e.Namn, &e.Datum, &beskrivning, &e.CreatedAt, &e.UpdatedAt)
		e.JaktstartType = "none"
		// Set default value for discard_after_heats
		e.DiscardAfterHeats = 0
	}

	// If there was no error getting the event, process the data
	if err == nil {
		// Set the beskrivning field to an empty string if it's NULL
		if beskrivning.Valid {
			e.Beskrivning = beskrivning.String
		} else {
			e.Beskrivning = ""
		}

		// Get participant count for this event
		participantCount, countErr := db.CountEventParticipants(e.ID)
		if countErr == nil {
			e.ParticipantCount = participantCount
		}
	}

	return e, err
}

// CreateEvent adds a new event to the database
func (db *DB) CreateEvent(namn string, datum time.Time, starttid string, vind, banlangd int, jaktstart bool, tavlingstyp, beskrivning string) (int64, error) {
	return db.CreateEventWithEntypsegling(namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, beskrivning, false, "")
}

// CreateEventWithJaktstartType adds a new event to the database with jaktstart_type support
func (db *DB) CreateEventWithJaktstartType(namn string, datum time.Time, starttid string, vind, banlangd int, jaktstartType, tavlingstyp, beskrivning, boatType string) (int64, error) {
	return db.CreateEventWithDiscardSupport(namn, datum, starttid, vind, banlangd, jaktstartType, tavlingstyp, beskrivning, boatType, 0)
}

// CreateEventWithDiscardSupport adds a new event to the database with discard_after_heats support
func (db *DB) CreateEventWithDiscardSupport(namn string, datum time.Time, starttid string, vind, banlangd int, jaktstartType, tavlingstyp, beskrivning, boatType string, discardAfterHeats int) (int64, error) {
	now := time.Now()

	// Check if the events table has the jaktstart_type column
	var hasJaktstartType int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart_type'
	`).Scan(&hasJaktstartType)

	if err != nil {
		return 0, err
	}

	// Check if the events table has the discard_after_heats column
	var hasDiscardAfterHeats int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'discard_after_heats'
	`).Scan(&hasDiscardAfterHeats)

	if err != nil {
		return 0, err
	}

	var result sql.Result

	// If discard_after_heats column exists, use the newest schema
	if hasJaktstartType > 0 && hasDiscardAfterHeats > 0 {
		result, err = db.Exec(`
			INSERT INTO events (namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boat_type, jaktstart_type, discard_after_heats, beskrivning, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, namn, datum, starttid, vind, banlangd,
			jaktstartType == "regular" || jaktstartType == "half", // Set legacy jaktstart field
			tavlingstyp,
			jaktstartType == "entypsegling", // Set legacy entypsegling field
			boatType,
			jaktstartType,
			discardAfterHeats,
			beskrivning, now, now)
	} else if hasJaktstartType > 0 {
		result, err = db.Exec(`
			INSERT INTO events (namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boat_type, jaktstart_type, beskrivning, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, namn, datum, starttid, vind, banlangd,
			jaktstartType == "regular" || jaktstartType == "half", // Set legacy jaktstart field
			tavlingstyp,
			jaktstartType == "entypsegling", // Set legacy entypsegling field
			boatType,
			jaktstartType,
			beskrivning, now, now)
	} else {
		// Fall back to legacy method for backward compatibility
		return db.CreateEventWithEntypsegling(namn, datum, starttid, vind, banlangd,
			jaktstartType == "regular" || jaktstartType == "half",
			tavlingstyp, beskrivning,
			jaktstartType == "entypsegling",
			boatType)
	}

	if err != nil {
		return 0, err
	}

	// Get the event ID
	eventID, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	log.Printf("DEBUG: CreateEventWithJaktstartType - Created event with ID: %d, jaktstartType: %s", eventID, jaktstartType)

	// Ensure the event has a default heat
	err = db.EnsureDefaultHeat(eventID)
	if err != nil {
		log.Printf("Warning: Failed to create default heat for event %d: %v", eventID, err)
		// Don't return error here as the event was created successfully
	}

	// Check if automatic backups are enabled
	autoBackup, err := db.GetSetting("auto_backup_on_event_create")
	if err != nil {
		log.Printf("DEBUG: CreateEventWithJaktstartType - Could not get auto_backup_on_event_create setting: %v", err)
	} else {
		log.Printf("DEBUG: CreateEventWithJaktstartType - auto_backup_on_event_create setting: %s", autoBackup)

		if autoBackup == "true" {
			// Checkpoint WAL before creating backup to ensure all changes are in the main DB file
			log.Printf("DEBUG: CreateEventWithJaktstartType - Performing WAL checkpoint before backup")
			err := db.CheckpointWAL()
			if err != nil {
				log.Printf("DEBUG: CreateEventWithJaktstartType - Failed to checkpoint WAL before backup: %v", err)
				// Continue anyway, backup might still be useful
			}

			// Try to get the database file path
			dbPath := ""
			var seq int
			var name string
			err = db.QueryRow("PRAGMA database_list").Scan(&seq, &name, &dbPath)
			if err != nil {
				log.Printf("DEBUG: CreateEventWithJaktstartType - Could not get database path for backup: %v", err)
				// Even if we can't get the path from PRAGMA, try with a default path
				dbPath = "segling.db"
				log.Printf("DEBUG: CreateEventWithJaktstartType - Using default database path: %s", dbPath)
			} else {
				log.Printf("DEBUG: CreateEventWithJaktstartType - Database path: %s", dbPath)
			}

			if dbPath != "" && dbPath != ":memory:" {
				// Create a backup of the database
				backupPath, err := db.CreateDatabaseBackup(dbPath)
				if err != nil {
					log.Printf("DEBUG: CreateEventWithJaktstartType - Failed to create database backup: %v", err)
				} else {
					log.Printf("DEBUG: CreateEventWithJaktstartType - Created database backup at: %s", backupPath)
				}
			} else {
				log.Printf("DEBUG: CreateEventWithJaktstartType - Database path is empty or in-memory, skipping backup")
			}
		} else {
			log.Printf("DEBUG: CreateEventWithJaktstartType - Automatic backups are disabled")
		}
	}

	return eventID, nil
}

// CreateEventWithEntypsegling adds a new event to the database with entypsegling support
func (db *DB) CreateEventWithEntypsegling(namn string, datum time.Time, starttid string, vind, banlangd int, jaktstart bool, tavlingstyp, beskrivning string, entypsegling bool, boatType string) (int64, error) {
	now := time.Now()

	// Check if the events table has the starttid column
	var hasStarttid int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'starttid'
	`).Scan(&hasStarttid)

	if err != nil {
		return 0, err
	}

	// Check if the events table has the jaktstart column
	var hasJaktstart int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart'
	`).Scan(&hasJaktstart)

	if err != nil {
		return 0, err
	}

	// Check if the events table has the tavlingstyp column
	var hasTavlingstyp int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'tavlingstyp'
	`).Scan(&hasTavlingstyp)

	if err != nil {
		return 0, err
	}

	// Check if the events table has the entypsegling column
	var hasEntypsegling int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'entypsegling'
	`).Scan(&hasEntypsegling)

	if err != nil {
		return 0, err
	}

	// Check if the events table has the boat_type column
	var hasBoatType int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'boat_type'
	`).Scan(&hasBoatType)

	if err != nil {
		return 0, err
	}

	var result sql.Result

	// If all columns exist, use the newest schema with entypsegling support
	if hasStarttid > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 && hasEntypsegling > 0 && hasBoatType > 0 {
		result, err = db.Exec(`
			INSERT INTO events (namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boat_type, beskrivning, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boatType, beskrivning, now, now)
	} else if hasStarttid > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 {
		result, err = db.Exec(`
			INSERT INTO events (namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, beskrivning, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, beskrivning, now, now)
	} else if hasStarttid > 0 && hasJaktstart > 0 {
		result, err = db.Exec(`
			INSERT INTO events (namn, datum, starttid, vind, banlangd, jaktstart, beskrivning, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, namn, datum, starttid, vind, banlangd, jaktstart, beskrivning, now, now)
	} else if hasStarttid > 0 {
		// If only starttid exists but not jaktstart, use the intermediate schema
		result, err = db.Exec(`
			INSERT INTO events (namn, datum, starttid, vind, banlangd, beskrivning, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?)
		`, namn, datum, starttid, vind, banlangd, beskrivning, now, now)
	} else {
		// Otherwise, use the old schema
		result, err = db.Exec(`
			INSERT INTO events (namn, datum, beskrivning, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?)
		`, namn, datum, beskrivning, now, now)
	}

	if err != nil {
		return 0, err
	}

	// Get the event ID
	eventID, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	log.Printf("DEBUG: CreateEvent - Created event with ID: %d", eventID)

	// Ensure the event has a default heat
	err = db.EnsureDefaultHeat(eventID)
	if err != nil {
		log.Printf("Warning: Failed to create default heat for event %d: %v", eventID, err)
		// Don't return error here as the event was created successfully
	}

	// Check if automatic backups are enabled
	autoBackup, err := db.GetSetting("auto_backup_on_event_create")
	if err != nil {
		log.Printf("DEBUG: CreateEvent - Could not get auto_backup_on_event_create setting: %v", err)
	} else {
		log.Printf("DEBUG: CreateEvent - auto_backup_on_event_create setting: %s", autoBackup)

		if autoBackup == "true" {
			// Checkpoint WAL before creating backup to ensure all changes are in the main DB file
			log.Printf("DEBUG: CreateEvent - Performing WAL checkpoint before backup")
			err := db.CheckpointWAL()
			if err != nil {
				log.Printf("DEBUG: CreateEvent - Failed to checkpoint WAL before backup: %v", err)
				// Continue anyway, backup might still be useful
			}

			// Try to get the database file path
			dbPath := ""
			var seq int
			var name string
			err = db.QueryRow("PRAGMA database_list").Scan(&seq, &name, &dbPath)
			if err != nil {
				log.Printf("DEBUG: CreateEvent - Could not get database path for backup: %v", err)
				// Even if we can't get the path from PRAGMA, try with a default path
				dbPath = "segling.db"
				log.Printf("DEBUG: CreateEvent - Using default database path: %s", dbPath)
			} else {
				log.Printf("DEBUG: CreateEvent - Database path: %s", dbPath)
			}

			if dbPath != "" && dbPath != ":memory:" {
				// Create a backup of the database
				backupPath, err := db.CreateDatabaseBackup(dbPath)
				if err != nil {
					log.Printf("DEBUG: CreateEvent - Failed to create database backup: %v", err)
				} else {
					log.Printf("DEBUG: CreateEvent - Created database backup at: %s", backupPath)
				}
			} else {
				log.Printf("DEBUG: CreateEvent - Database path is empty or in-memory, skipping backup")
			}
		} else {
			log.Printf("DEBUG: CreateEvent - Automatic backups are disabled")
		}
	}

	return eventID, nil
}

// UpdateEvent updates an existing event
func (db *DB) UpdateEvent(id int64, namn string, datum time.Time, starttid string, vind, banlangd int, jaktstart bool, tavlingstyp, beskrivning string) error {
	return db.UpdateEventWithEntypsegling(id, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, beskrivning, false, "")
}

// UpdateEventWithJaktstartType updates an existing event with jaktstart_type support
func (db *DB) UpdateEventWithJaktstartType(id int64, namn string, datum time.Time, starttid string, vind, banlangd int, jaktstartType, tavlingstyp, beskrivning, boatType string) error {
	return db.UpdateEventWithDiscardSupport(id, namn, datum, starttid, vind, banlangd, jaktstartType, tavlingstyp, beskrivning, boatType, 0)
}

// UpdateEventWithDiscardSupport updates an existing event with discard_after_heats support
func (db *DB) UpdateEventWithDiscardSupport(id int64, namn string, datum time.Time, starttid string, vind, banlangd int, jaktstartType, tavlingstyp, beskrivning, boatType string, discardAfterHeats int) error {
	now := time.Now()

	// Check if the events table has the jaktstart_type column
	var hasJaktstartType int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart_type'
	`).Scan(&hasJaktstartType)

	if err != nil {
		return err
	}

	// Check if the events table has the discard_after_heats column
	var hasDiscardAfterHeats int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'discard_after_heats'
	`).Scan(&hasDiscardAfterHeats)

	if err != nil {
		return err
	}

	// If discard_after_heats column exists, use the newest schema
	if hasJaktstartType > 0 && hasDiscardAfterHeats > 0 {
		_, err = db.Exec(`
			UPDATE events
			SET namn = ?, datum = ?, starttid = ?, vind = ?, banlangd = ?, jaktstart = ?, tavlingstyp = ?, entypsegling = ?, boat_type = ?, jaktstart_type = ?, discard_after_heats = ?, beskrivning = ?, updated_at = ?
			WHERE id = ?
		`, namn, datum, starttid, vind, banlangd,
			jaktstartType == "regular" || jaktstartType == "half", // Set legacy jaktstart field
			tavlingstyp,
			jaktstartType == "entypsegling", // Set legacy entypsegling field
			boatType,
			jaktstartType,
			discardAfterHeats,
			beskrivning, now, id)
	} else if hasJaktstartType > 0 {
		_, err = db.Exec(`
			UPDATE events
			SET namn = ?, datum = ?, starttid = ?, vind = ?, banlangd = ?, jaktstart = ?, tavlingstyp = ?, entypsegling = ?, boat_type = ?, jaktstart_type = ?, beskrivning = ?, updated_at = ?
			WHERE id = ?
		`, namn, datum, starttid, vind, banlangd,
			jaktstartType == "regular" || jaktstartType == "half", // Set legacy jaktstart field
			tavlingstyp,
			jaktstartType == "entypsegling", // Set legacy entypsegling field
			boatType,
			jaktstartType,
			beskrivning, now, id)
	} else {
		// Fall back to legacy method for backward compatibility
		return db.UpdateEventWithEntypsegling(id, namn, datum, starttid, vind, banlangd,
			jaktstartType == "regular" || jaktstartType == "half",
			tavlingstyp, beskrivning,
			jaktstartType == "entypsegling",
			boatType)
	}

	// Sync first heat start time with the event's start time so UI reflects updates
	if err == nil {
		if jaktstartType == "entypsegling" {
			// For entypsegling events, clear heat start time
			if _, syncErr := db.Exec(`UPDATE heats SET start_time = '' WHERE event_id = ? AND heat_number = 1`, id); syncErr != nil {
				log.Printf("WARNING: Failed to clear first heat start time for entypsegling event %d: %v", id, syncErr)
			}
		} else {
			// For regular events, set the first heat start time to the event start time
			if _, syncErr := db.Exec(`UPDATE heats SET start_time = ? WHERE event_id = ? AND heat_number = 1`, starttid, id); syncErr != nil {
				log.Printf("WARNING: Failed to sync first heat start time for event %d: %v", id, syncErr)
			}
		}
	}

	return err
}

// UpdateEventWithEntypsegling updates an existing event with entypsegling support
func (db *DB) UpdateEventWithEntypsegling(id int64, namn string, datum time.Time, starttid string, vind, banlangd int, jaktstart bool, tavlingstyp, beskrivning string, entypsegling bool, boatType string) error {
	// TEMPORARILY DISABLED: Check if the event is locked
	// var locked bool
	// err := db.QueryRow(`SELECT locked FROM events WHERE id = ?`, id).Scan(&locked)
	// if err != nil {
	// 	return err
	// }

	// If the event is locked, don't allow updates
	// if locked {
	// 	return fmt.Errorf("tävlingen är låst och kan inte uppdateras - lås upp tävlingen först")
	// }

	now := time.Now()

	// Check if the events table has the starttid column
	var hasStarttid int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'starttid'
	`).Scan(&hasStarttid)

	if err != nil {
		return err
	}

	// Check if the events table has the jaktstart column
	var hasJaktstart int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart'
	`).Scan(&hasJaktstart)

	if err != nil {
		return err
	}

	// Check if the events table has the tavlingstyp column
	var hasTavlingstyp int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'tavlingstyp'
	`).Scan(&hasTavlingstyp)

	if err != nil {
		return err
	}

	// Check if the events table has the entypsegling column
	var hasEntypsegling int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'entypsegling'
	`).Scan(&hasEntypsegling)

	if err != nil {
		return err
	}

	// Check if the events table has the boat_type column
	var hasBoatType int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'boat_type'
	`).Scan(&hasBoatType)

	if err != nil {
		return err
	}

	// If all columns exist, use the newest schema with entypsegling support
	if hasStarttid > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 && hasEntypsegling > 0 && hasBoatType > 0 {
		_, err = db.Exec(`
			UPDATE events
			SET namn = ?, datum = ?, starttid = ?, vind = ?, banlangd = ?, jaktstart = ?, tavlingstyp = ?, entypsegling = ?, boat_type = ?, beskrivning = ?, updated_at = ?
			WHERE id = ?
		`, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, entypsegling, boatType, beskrivning, now, id)
	} else if hasStarttid > 0 && hasJaktstart > 0 && hasTavlingstyp > 0 {
		_, err = db.Exec(`
			UPDATE events
			SET namn = ?, datum = ?, starttid = ?, vind = ?, banlangd = ?, jaktstart = ?, tavlingstyp = ?, beskrivning = ?, updated_at = ?
			WHERE id = ?
		`, namn, datum, starttid, vind, banlangd, jaktstart, tavlingstyp, beskrivning, now, id)
	} else if hasStarttid > 0 && hasJaktstart > 0 {
		_, err = db.Exec(`
			UPDATE events
			SET namn = ?, datum = ?, starttid = ?, vind = ?, banlangd = ?, jaktstart = ?, beskrivning = ?, updated_at = ?
			WHERE id = ?
		`, namn, datum, starttid, vind, banlangd, jaktstart, beskrivning, now, id)
	} else if hasStarttid > 0 {
		// If only starttid exists but not jaktstart, use the intermediate schema
		_, err = db.Exec(`
			UPDATE events
			SET namn = ?, datum = ?, starttid = ?, vind = ?, banlangd = ?, beskrivning = ?, updated_at = ?
			WHERE id = ?
		`, namn, datum, starttid, vind, banlangd, beskrivning, now, id)
	} else {
		// Otherwise, use the old schema
		_, err = db.Exec(`
			UPDATE events
			SET namn = ?, datum = ?, beskrivning = ?, updated_at = ?
			WHERE id = ?
		`, namn, datum, beskrivning, now, id)
	}

	// After updating the event, sync the first heat's start time to match the event
	if err == nil {
		if entypsegling {
			// For entypsegling events, clear heat start time
			if _, syncErr := db.Exec(`UPDATE heats SET start_time = '' WHERE event_id = ? AND heat_number = 1`, id); syncErr != nil {
				log.Printf("WARNING: Failed to clear first heat start time for entypsegling event %d: %v", id, syncErr)
			}
		} else {
			// For regular events, set to event start time
			if _, syncErr := db.Exec(`UPDATE heats SET start_time = ? WHERE event_id = ? AND heat_number = 1`, starttid, id); syncErr != nil {
				log.Printf("WARNING: Failed to sync first heat start time for event %d: %v", id, syncErr)
			}
		}
	}

	return err
}

// DeleteEvent removes an event from the database
func (db *DB) DeleteEvent(id int64) error {
	_, err := db.Exec(`DELETE FROM events WHERE id = ?`, id)
	return err
}

// AddParticipantToEvent adds a sailor with a boat to an event
func (db *DB) AddParticipantToEvent(eventID, sailorID, boatID int64, srsType string, selectedSRSValue, customSRSValue float64, useCustomSRSValue bool) (int64, error) {
	// TEMPORARILY DISABLED: Check if the event is locked
	// var locked bool
	// err := db.QueryRow(`SELECT locked FROM events WHERE id = ?`, eventID).Scan(&locked)
	// if err != nil {
	// 	return 0, fmt.Errorf("failed to check if event is locked: %w", err)
	// }

	// If the event is locked, don't allow adding participants
	// if locked {
	// 	return 0, fmt.Errorf("tävlingen är låst och deltagare kan inte läggas till - lås upp tävlingen först")
	// }
	// Check if the event_participants table has the srs_type column
	var count int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'srs_type'
	`).Scan(&count)

	if err != nil {
		return 0, err
	}

	// Check if the event_participants table has the crew_count column
	var hasCrewCount int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'crew_count'
	`).Scan(&hasCrewCount)

	if err != nil {
		return 0, err
	}

	now := time.Now()
	var result sql.Result

	// If both columns exist, use the newest schema
	if count > 0 && hasCrewCount > 0 {
		result, err = db.Exec(`
			INSERT INTO event_participants (
				event_id, sailor_id, boat_id,
				srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
				crew_count, created_at, updated_at
			)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, eventID, sailorID, boatID, srsType, selectedSRSValue, customSRSValue, useCustomSRSValue, 0, now, now)
	} else if count > 0 {
		// If only srs_type exists but not crew_count, use the intermediate schema
		result, err = db.Exec(`
			INSERT INTO event_participants (
				event_id, sailor_id, boat_id,
				srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
				created_at, updated_at
			)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, eventID, sailorID, boatID, srsType, selectedSRSValue, customSRSValue, useCustomSRSValue, now, now)
	} else {
		// Otherwise, use the old schema
		result, err = db.Exec(`
			INSERT INTO event_participants (
				event_id, sailor_id, boat_id,
				created_at, updated_at
			)
			VALUES (?, ?, ?, ?, ?)
		`, eventID, sailorID, boatID, now, now)
	}

	if err != nil {
		return 0, err
	}

	return result.LastInsertId()
}

// AddEntypSeglingParticipantToEvent adds a sailor with personal number to an entypsegling event
func (db *DB) AddEntypSeglingParticipantToEvent(eventID, sailorID int64, personalNumber string, crewCount int) (int64, error) {
	// Check if the event_participants table has the personal_number column
	var hasPersonalNumber int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'personal_number'
	`).Scan(&hasPersonalNumber)

	if err != nil {
		return 0, err
	}

	// Check if the event_participants table has the crew_count column
	var hasCrewCount int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'crew_count'
	`).Scan(&hasCrewCount)

	if err != nil {
		return 0, err
	}

	now := time.Now()
	var result sql.Result

	// For entypsegling events, boat_id can be NULL and we use personal_number instead
	if hasPersonalNumber > 0 && hasCrewCount > 0 {
		result, err = db.Exec(`
			INSERT INTO event_participants (
				event_id, sailor_id, boat_id, personal_number, crew_count, created_at, updated_at
			)
			VALUES (?, ?, NULL, ?, ?, ?, ?)
		`, eventID, sailorID, personalNumber, crewCount, now, now)
	} else if hasPersonalNumber > 0 {
		result, err = db.Exec(`
			INSERT INTO event_participants (
				event_id, sailor_id, boat_id, personal_number, created_at, updated_at
			)
			VALUES (?, ?, NULL, ?, ?, ?)
		`, eventID, sailorID, personalNumber, now, now)
	} else {
		// Fallback for older schema - this shouldn't happen for entypsegling events
		return 0, fmt.Errorf("personal_number column not found - cannot add entypsegling participant")
	}

	if err != nil {
		return 0, err
	}

	return result.LastInsertId()
}

// UpdateEntypSeglingParticipant updates personal number and crew count for an entypsegling participant
func (db *DB) UpdateEntypSeglingParticipant(participantID int64, personalNumber string, crewCount int) error {
	now := time.Now()

	_, err := db.Exec(`
		UPDATE event_participants
		SET personal_number = ?, crew_count = ?, updated_at = ?
		WHERE id = ?
	`, personalNumber, crewCount, now, participantID)

	return err
}

// RemoveParticipantFromEvent removes a participant from an event
func (db *DB) RemoveParticipantFromEvent(id int64) error {
	// Get the participant to check the event
	participant, err := db.GetParticipant(id)
	if err != nil {
		return fmt.Errorf("failed to get participant: %w", err)
	}

	// Check if the event is locked
	var locked bool
	err = db.QueryRow(`SELECT locked FROM events WHERE id = ?`, participant.EventID).Scan(&locked)
	if err != nil {
		return fmt.Errorf("failed to check if event is locked: %w", err)
	}

	// If the event is locked, don't allow removing participants
	if locked {
		return fmt.Errorf("cannot remove participants from locked event")
	}

	_, err = db.Exec(`DELETE FROM event_participants WHERE id = ?`, id)
	return err
}

// UpdateSRSValueFromType returns the correct SRS value for a boat based on the SRS type
func (db *DB) UpdateSRSValueFromType(boat models.Boat, srsType string) float64 {
	var srsValue float64

	log.Printf("DEBUG: UpdateSRSValueFromType - Boat ID: %d, Name: %s, Type: %s", boat.ID, boat.Namn, boat.Battyp)
	log.Printf("DEBUG: UpdateSRSValueFromType - SRS Type: %s", srsType)
	log.Printf("DEBUG: UpdateSRSValueFromType - Boat SRS values: SRS=%f, SRSUtanUndanvindsegel=%f, SRSShorthanded=%f, SRSShorthandedUtanUndanvindsegel=%f",
		boat.SRS, boat.SRSUtanUndanvindsegel, boat.SRSShorthanded, boat.SRSShorthandedUtanUndanvindsegel)

	switch srsType {
	case "srs_utan_undanvindsegel":
		srsValue = boat.SRSUtanUndanvindsegel
		log.Printf("DEBUG: UpdateSRSValueFromType - Using SRSUtanUndanvindsegel: %f", srsValue)
	case "srs_shorthanded":
		srsValue = boat.SRSShorthanded
		log.Printf("DEBUG: UpdateSRSValueFromType - Using SRSShorthanded: %f", srsValue)
	case "srs_shorthanded_utan_undanvindsegel":
		srsValue = boat.SRSShorthandedUtanUndanvindsegel
		log.Printf("DEBUG: UpdateSRSValueFromType - Using SRSShorthandedUtanUndanvindsegel: %f", srsValue)
	default:
		srsValue = boat.SRS
		log.Printf("DEBUG: UpdateSRSValueFromType - Using default SRS: %f", srsValue)
	}

	return srsValue
}

// UpdateParticipantSRS updates the SRS values for a participant
func (db *DB) UpdateParticipantSRS(id int64, srsType string, selectedSRSValue, customSRSValue float64, useCustomSRSValue bool, crewCount int) error {
	now := time.Now()

	// Get the participant to access its boat
	participant, err := db.GetParticipant(id)
	if err != nil {
		log.Printf("ERROR: UpdateParticipantSRS - Failed to get participant %d: %v", id, err)
		return err
	}

	// Check if the event is locked
	var locked bool
	err = db.QueryRow(`SELECT locked FROM events WHERE id = ?`, participant.EventID).Scan(&locked)
	if err != nil {
		return fmt.Errorf("failed to check if event is locked: %w", err)
	}

	// If the event is locked, don't allow updating participants
	if locked {
		return fmt.Errorf("tävlingen är låst och deltagare kan inte uppdateras - lås upp tävlingen först")
	}

	// Get the boat to access its SRS values
	boat, err := db.GetBoat(participant.BoatID)
	if err != nil {
		log.Printf("ERROR: UpdateParticipantSRS - Failed to get boat %d: %v", participant.BoatID, err)
		return err
	}

	// Calculate the correct SRS value based on the SRS type
	srsValue := db.UpdateSRSValueFromType(boat, srsType)
	log.Printf("DEBUG: UpdateParticipantSRS - Participant ID: %d, SRS Type: %s, Calculated SRS Value: %f", id, srsType, srsValue)

	// Check if the event_participants table has the crew_count column
	var hasCrewCount int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'crew_count'
	`).Scan(&hasCrewCount)

	if err != nil {
		log.Printf("ERROR: UpdateParticipantSRS - Failed to check if crew_count column exists: %v", err)
		return err
	}

	var updateErr error
	if hasCrewCount > 0 {
		_, updateErr = db.Exec(`
			UPDATE event_participants
			SET srs_type = ?, selected_srs_value = ?, custom_srs_value = ?, use_custom_srs_value = ?, crew_count = ?, updated_at = ?
			WHERE id = ?
		`, srsType, srsValue, customSRSValue, useCustomSRSValue, crewCount, now, id)
	} else {
		_, updateErr = db.Exec(`
			UPDATE event_participants
			SET srs_type = ?, selected_srs_value = ?, custom_srs_value = ?, use_custom_srs_value = ?, updated_at = ?
			WHERE id = ?
		`, srsType, srsValue, customSRSValue, useCustomSRSValue, now, id)
	}

	if updateErr != nil {
		log.Printf("ERROR: UpdateParticipantSRS - Failed to update participant %d: %v", id, updateErr)
	} else {
		log.Printf("DEBUG: UpdateParticipantSRS - Successfully updated participant %d with SRS type %s, SRS value %f, and crew count %d", id, srsType, srsValue, crewCount)
	}

	return updateErr
}

// GetParticipant returns a single participant by ID
func (db *DB) GetParticipant(id int64) (models.EventParticipant, error) {
	var p models.EventParticipant

	// Check if the event_participants table has the srs_type column
	var count int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'srs_type'
	`).Scan(&count)

	if err != nil {
		return p, err
	}

	// Check if the event_participants table has the finish_time column
	var hasFinishTime int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'finish_time'
	`).Scan(&hasFinishTime)

	if err != nil {
		return p, err
	}

	// Check if the event_participants table has the crew_count column
	var hasCrewCount int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'crew_count'
	`).Scan(&hasCrewCount)

	if err != nil {
		return p, err
	}

	// Check if the event_participants table has the personal_number column
	var hasPersonalNumber int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'personal_number'
	`).Scan(&hasPersonalNumber)

	if err != nil {
		return p, err
	}

	// Check if the event_participants table has the dns and dnf columns
	var hasDNSAndDNF int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name IN ('dns', 'dnf')
	`).Scan(&hasDNSAndDNF)

	if err != nil {
		return p, err
	}

	// If all columns exist, use the newest schema
	if count > 0 && hasFinishTime > 0 && hasCrewCount > 0 {
		// Use nullable types for the SRS fields and boat_id
		var srsType sql.NullString
		var selectedSRSValue, customSRSValue sql.NullFloat64
		var useCustomSRSValue sql.NullBool
		var finishTime sql.NullString
		var crewCount sql.NullInt64
		var dns, dnf sql.NullBool
		var boatID sql.NullInt64
		var personalNumber sql.NullString

		// If personal_number and DNS/DNF columns exist, include them in the query
		if hasPersonalNumber > 0 && hasDNSAndDNF > 0 {
			err = db.QueryRow(`
				SELECT id, event_id, sailor_id, boat_id, personal_number,
					   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
					   finish_time, crew_count, dns, dnf, created_at, updated_at
				FROM event_participants
				WHERE id = ?
			`, id).Scan(
				&p.ID, &p.EventID, &p.SailorID, &boatID, &personalNumber,
				&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
				&finishTime, &crewCount, &dns, &dnf, &p.CreatedAt, &p.UpdatedAt,
			)
		} else if hasPersonalNumber > 0 {
			err = db.QueryRow(`
				SELECT id, event_id, sailor_id, boat_id, personal_number,
					   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
					   finish_time, crew_count, created_at, updated_at
				FROM event_participants
				WHERE id = ?
			`, id).Scan(
				&p.ID, &p.EventID, &p.SailorID, &boatID, &personalNumber,
				&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
				&finishTime, &crewCount, &p.CreatedAt, &p.UpdatedAt,
			)
		} else if hasDNSAndDNF > 0 {
			err = db.QueryRow(`
				SELECT id, event_id, sailor_id, boat_id,
					   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
					   finish_time, crew_count, dns, dnf, created_at, updated_at
				FROM event_participants
				WHERE id = ?
			`, id).Scan(
				&p.ID, &p.EventID, &p.SailorID, &boatID,
				&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
				&finishTime, &crewCount, &dns, &dnf, &p.CreatedAt, &p.UpdatedAt,
			)
		} else {
			err = db.QueryRow(`
				SELECT id, event_id, sailor_id, boat_id,
					   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
					   finish_time, crew_count, created_at, updated_at
				FROM event_participants
				WHERE id = ?
			`, id).Scan(
				&p.ID, &p.EventID, &p.SailorID, &boatID,
				&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
				&finishTime, &crewCount, &p.CreatedAt, &p.UpdatedAt,
			)
		}

		// Set boat ID if valid
		if boatID.Valid {
			p.BoatID = boatID.Int64
		} else {
			p.BoatID = 0
		}

		// Set personal number if valid
		if personalNumber.Valid {
			p.PersonalNumber = personalNumber.String
		}

		if err != nil {
			return p, err
		}

		// Set DNS and DNF values if they exist
		if hasDNSAndDNF > 0 {
			if dns.Valid {
				p.DNS = dns.Bool
				log.Printf("DEBUG: GetParticipant - Participant %d DNS status: %v", p.ID, p.DNS)
			}

			if dnf.Valid {
				p.DNF = dnf.Bool
				log.Printf("DEBUG: GetParticipant - Participant %d DNF status: %v", p.ID, p.DNF)
			}
		}

		// Set finish time if valid
		if finishTime.Valid {
			p.FinishTime = finishTime.String
		}

		// Set crew_count value
		if crewCount.Valid {
			p.CrewCount = int(crewCount.Int64)
		} else {
			p.CrewCount = 0
		}

		// Set SRS values if valid
		if srsType.Valid {
			p.SRSType = srsType.String
			log.Printf("DEBUG: GetParticipant - Participant %d has SRS type: %s", p.ID, p.SRSType)
		} else {
			p.SRSType = "srs"
			log.Printf("DEBUG: GetParticipant - Participant %d has NULL SRS type, defaulting to 'srs'", p.ID)
		}

		// Get the boat to set the correct SRS value based on SRS type
		boat, err := db.GetBoat(p.BoatID)
		if err == nil {
			// Determine which SRS value to use based on the SRS type
			var boatSRSValue float64
			switch p.SRSType {
			case "srs_utan_undanvindsegel":
				boatSRSValue = boat.SRSUtanUndanvindsegel
				log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
			case "srs_shorthanded":
				boatSRSValue = boat.SRSShorthanded
				log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_shorthanded', boat SRS value: %f", p.ID, boatSRSValue)
			case "srs_shorthanded_utan_undanvindsegel":
				boatSRSValue = boat.SRSShorthandedUtanUndanvindsegel
				log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_shorthanded_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
			default:
				boatSRSValue = boat.SRS
				log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs', boat SRS value: %f", p.ID, boatSRSValue)
			}

			// Always use the boat's SRS value based on the SRS type
			p.SelectedSRSValue = boatSRSValue
			log.Printf("DEBUG: GetParticipant - Participant %d using boat SRS value based on SRS type: %f", p.ID, p.SelectedSRSValue)
		} else if selectedSRSValue.Valid {
			// If we couldn't get the boat but have a valid selectedSRSValue, use it
			p.SelectedSRSValue = selectedSRSValue.Float64
			log.Printf("DEBUG: GetParticipant - Participant %d could not get boat, using stored selected SRS value: %f", p.ID, p.SelectedSRSValue)
		} else {
			log.Printf("DEBUG: GetParticipant - Participant %d has NULL selected SRS value and could not get boat SRS: %v", p.ID, err)
		}

		if customSRSValue.Valid {
			p.CustomSRSValue = customSRSValue.Float64
			log.Printf("DEBUG: GetParticipant - Participant %d has custom SRS value: %f", p.ID, p.CustomSRSValue)
		} else {
			log.Printf("DEBUG: GetParticipant - Participant %d has NULL custom SRS value", p.ID)
		}

		if useCustomSRSValue.Valid {
			p.UseCustomSRSValue = useCustomSRSValue.Bool
			log.Printf("DEBUG: GetParticipant - Participant %d uses custom SRS value: %v", p.ID, p.UseCustomSRSValue)
		} else {
			log.Printf("DEBUG: GetParticipant - Participant %d has NULL use_custom_srs_value", p.ID)
		}
	} else if count > 0 && hasFinishTime > 0 {
		// Use nullable types for the SRS fields
		var srsType sql.NullString
		var selectedSRSValue, customSRSValue sql.NullFloat64
		var useCustomSRSValue sql.NullBool
		var finishTime sql.NullString

		err := db.QueryRow(`
			SELECT id, event_id, sailor_id, boat_id,
				   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
				   finish_time, created_at, updated_at
			FROM event_participants
			WHERE id = ?
		`, id).Scan(
			&p.ID, &p.EventID, &p.SailorID, &p.BoatID,
			&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
			&finishTime, &p.CreatedAt, &p.UpdatedAt,
		)

		if err != nil {
			return p, err
		}

		// Set finish time if valid
		if finishTime.Valid {
			p.FinishTime = finishTime.String
		}

		// Set the values in the participant struct
		if srsType.Valid {
			p.SRSType = srsType.String
		}
		if selectedSRSValue.Valid {
			p.SelectedSRSValue = selectedSRSValue.Float64
		}
		if customSRSValue.Valid {
			p.CustomSRSValue = customSRSValue.Float64
		}
		if useCustomSRSValue.Valid {
			p.UseCustomSRSValue = useCustomSRSValue.Bool
		}
	} else if count > 0 {
		// Use nullable types for the SRS fields
		var srsType sql.NullString
		var selectedSRSValue, customSRSValue sql.NullFloat64
		var useCustomSRSValue sql.NullBool

		err := db.QueryRow(`
			SELECT id, event_id, sailor_id, boat_id,
				   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
				   created_at, updated_at
			FROM event_participants
			WHERE id = ?
		`, id).Scan(
			&p.ID, &p.EventID, &p.SailorID, &p.BoatID,
			&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
			&p.CreatedAt, &p.UpdatedAt,
		)

		if err != nil {
			return p, err
		}

		// Set default values if NULL
		if srsType.Valid {
			p.SRSType = srsType.String
			log.Printf("DEBUG: GetParticipant - Participant %d has SRS type: %s", p.ID, p.SRSType)
		} else {
			p.SRSType = "srs"
			log.Printf("DEBUG: GetParticipant - Participant %d has NULL SRS type, defaulting to 'srs'", p.ID)
		}

		// Get the boat to set the correct SRS value based on SRS type
		boat, err := db.GetBoat(p.BoatID)
		if err == nil {
			// If selectedSRSValue is valid, use it only if it matches the boat's SRS value for the given SRS type
			// Otherwise, set it based on the SRS type
			var boatSRSValue float64
			switch p.SRSType {
			case "srs_utan_undanvindsegel":
				boatSRSValue = boat.SRSUtanUndanvindsegel
				log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
			case "srs_shorthanded":
				boatSRSValue = boat.SRSShorthanded
				log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_shorthanded', boat SRS value: %f", p.ID, boatSRSValue)
			case "srs_shorthanded_utan_undanvindsegel":
				boatSRSValue = boat.SRSShorthandedUtanUndanvindsegel
				log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_shorthanded_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
			default:
				boatSRSValue = boat.SRS
				log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs', boat SRS value: %f", p.ID, boatSRSValue)
			}

			// Always use the boat's SRS value based on the SRS type
			p.SelectedSRSValue = boatSRSValue
			log.Printf("DEBUG: GetParticipant - Participant %d using boat SRS value based on SRS type: %f", p.ID, p.SelectedSRSValue)
		} else if selectedSRSValue.Valid {
			// If we couldn't get the boat but have a valid selectedSRSValue, use it
			p.SelectedSRSValue = selectedSRSValue.Float64
			log.Printf("DEBUG: GetParticipant - Participant %d could not get boat, using stored selected SRS value: %f", p.ID, p.SelectedSRSValue)
		} else {
			log.Printf("DEBUG: GetParticipant - Participant %d has NULL selected SRS value and could not get boat SRS: %v", p.ID, err)
		}

		if customSRSValue.Valid {
			p.CustomSRSValue = customSRSValue.Float64
			log.Printf("DEBUG: GetParticipant - Participant %d has custom SRS value: %f", p.ID, p.CustomSRSValue)
		} else {
			log.Printf("DEBUG: GetParticipant - Participant %d has NULL custom SRS value", p.ID)
		}

		if useCustomSRSValue.Valid {
			p.UseCustomSRSValue = useCustomSRSValue.Bool
			log.Printf("DEBUG: GetParticipant - Participant %d uses custom SRS value: %v", p.ID, p.UseCustomSRSValue)
		} else {
			log.Printf("DEBUG: GetParticipant - Participant %d has NULL use_custom_srs_value", p.ID)
		}
	} else {
		// Otherwise, use the old schema
		err := db.QueryRow(`
			SELECT id, event_id, sailor_id, boat_id, created_at, updated_at
			FROM event_participants
			WHERE id = ?
		`, id).Scan(
			&p.ID, &p.EventID, &p.SailorID, &p.BoatID, &p.CreatedAt, &p.UpdatedAt,
		)

		if err != nil {
			return p, err
		}

		// Get the boat to set the default SRS value
		boat, err := db.GetBoat(p.BoatID)
		if err == nil {
			p.SRSType = "srs"
			p.SelectedSRSValue = boat.SRS
		}
	}

	return p, nil
}

// GetEventParticipants returns all participants for an event
func (db *DB) GetEventParticipants(eventID int64) ([]models.EventParticipant, error) {
	// Check if the event_participants table has the srs_type column
	var count int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'srs_type'
	`).Scan(&count)

	if err != nil {
		return nil, err
	}

	// Debug log
	log.Printf("GetEventParticipants for event %d - srs_type column exists: %v", eventID, count > 0)

	var rows *sql.Rows

	// Check if the event_participants table has the finish_time column
	var hasFinishTime int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'finish_time'
	`).Scan(&hasFinishTime)

	if err != nil {
		return nil, err
	}

	// Check if the event_participants table has the crew_count column
	var hasCrewCount int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'crew_count'
	`).Scan(&hasCrewCount)

	if err != nil {
		return nil, err
	}

	// Check if the event_participants table has the personal_number column
	var hasPersonalNumber int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'personal_number'
	`).Scan(&hasPersonalNumber)

	if err != nil {
		return nil, err
	}

	// Check if the event_participants table has the dns and dnf columns
	var hasDNSAndDNF int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name IN ('dns', 'dnf')
	`).Scan(&hasDNSAndDNF)

	if err != nil {
		return nil, err
	}

	// If all columns exist, use the newest schema
	if count > 0 && hasFinishTime > 0 && hasCrewCount > 0 {
		log.Printf("Using schema with srs_type, finish_time, and crew_count columns")

		// If personal_number column exists, include it in the query
		if hasPersonalNumber > 0 && hasDNSAndDNF > 0 {
			log.Printf("Including personal_number, DNS and DNF columns in the query")
			rows, err = db.Query(`
				SELECT id, event_id, sailor_id, boat_id, personal_number,
					   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
					   finish_time, crew_count, dns, dnf, created_at, updated_at
				FROM event_participants
				WHERE event_id = ?
			`, eventID)
		} else if hasPersonalNumber > 0 {
			log.Printf("Including personal_number column in the query")
			rows, err = db.Query(`
				SELECT id, event_id, sailor_id, boat_id, personal_number,
					   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
					   finish_time, crew_count, created_at, updated_at
				FROM event_participants
				WHERE event_id = ?
			`, eventID)
		} else if hasDNSAndDNF > 0 {
			log.Printf("Including DNS and DNF columns in the query")
			rows, err = db.Query(`
				SELECT id, event_id, sailor_id, boat_id,
					   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
					   finish_time, crew_count, dns, dnf, created_at, updated_at
				FROM event_participants
				WHERE event_id = ?
			`, eventID)
		} else {
			rows, err = db.Query(`
				SELECT id, event_id, sailor_id, boat_id,
					   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
					   finish_time, crew_count, created_at, updated_at
				FROM event_participants
				WHERE event_id = ?
			`, eventID)
		}
	} else if count > 0 && hasFinishTime > 0 {
		log.Printf("Using schema with both srs_type and finish_time columns")
		rows, err = db.Query(`
			SELECT id, event_id, sailor_id, boat_id,
				   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
				   finish_time, created_at, updated_at
			FROM event_participants
			WHERE event_id = ?
		`, eventID)
	} else if count > 0 {
		// If only srs_type exists but not finish_time, use the intermediate schema
		log.Printf("Using schema with only srs_type column")
		rows, err = db.Query(`
			SELECT id, event_id, sailor_id, boat_id,
				   srs_type, selected_srs_value, custom_srs_value, use_custom_srs_value,
				   created_at, updated_at
			FROM event_participants
			WHERE event_id = ?
		`, eventID)
	} else {
		// Otherwise, use the old schema
		rows, err = db.Query(`
			SELECT id, event_id, sailor_id, boat_id,
				   created_at, updated_at
			FROM event_participants
			WHERE event_id = ?
		`, eventID)
	}

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var participants []models.EventParticipant
	for rows.Next() {
		var p models.EventParticipant

		// Default values for SRS fields
		p.SRSType = "srs"
		p.SelectedSRSValue = 0
		p.CustomSRSValue = 0
		p.UseCustomSRSValue = false

		// If all columns exist, use the newest schema
		if count > 0 && hasFinishTime > 0 && hasCrewCount > 0 {
			// Use nullable types for the SRS fields and boat_id (for entypsegling events)
			var srsType sql.NullString
			var selectedSRSValue, customSRSValue sql.NullFloat64
			var useCustomSRSValue sql.NullBool
			var finishTime sql.NullString
			var crewCount sql.NullInt64
			var dns, dnf sql.NullBool
			var personalNumber sql.NullString
			var boatID sql.NullInt64 // Make boat_id nullable for entypsegling events

			// If personal_number and DNS/DNF columns exist, include them in the scan
			if hasPersonalNumber > 0 && hasDNSAndDNF > 0 {
				err := rows.Scan(
					&p.ID, &p.EventID, &p.SailorID, &boatID, &personalNumber,
					&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
					&finishTime, &crewCount, &dns, &dnf, &p.CreatedAt, &p.UpdatedAt,
				)
				if err != nil {
					return nil, err
				}

				// Set boat ID if valid (for regular events)
				if boatID.Valid {
					p.BoatID = boatID.Int64
				} else {
					p.BoatID = 0 // Default for entypsegling events
				}

				// Set personal number if valid
				if personalNumber.Valid {
					p.PersonalNumber = personalNumber.String
				}

				// Set DNS and DNF values if valid
				if dns.Valid {
					p.DNS = dns.Bool
				}

				if dnf.Valid {
					p.DNF = dnf.Bool
				}
			} else if hasPersonalNumber > 0 {
				err := rows.Scan(
					&p.ID, &p.EventID, &p.SailorID, &boatID, &personalNumber,
					&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
					&finishTime, &crewCount, &p.CreatedAt, &p.UpdatedAt,
				)
				if err != nil {
					return nil, err
				}

				// Set boat ID if valid (for regular events)
				if boatID.Valid {
					p.BoatID = boatID.Int64
				} else {
					p.BoatID = 0 // Default for entypsegling events
				}

				// Set personal number if valid
				if personalNumber.Valid {
					p.PersonalNumber = personalNumber.String
				}
			} else if hasDNSAndDNF > 0 {
				err := rows.Scan(
					&p.ID, &p.EventID, &p.SailorID, &boatID,
					&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
					&finishTime, &crewCount, &dns, &dnf, &p.CreatedAt, &p.UpdatedAt,
				)
				if err != nil {
					return nil, err
				}

				// Set boat ID if valid (for regular events)
				if boatID.Valid {
					p.BoatID = boatID.Int64
				} else {
					p.BoatID = 0 // Default for entypsegling events
				}

				// Set DNS and DNF values if valid
				if dns.Valid {
					p.DNS = dns.Bool
				}

				if dnf.Valid {
					p.DNF = dnf.Bool
				}
			} else {
				err := rows.Scan(
					&p.ID, &p.EventID, &p.SailorID, &boatID,
					&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
					&finishTime, &crewCount, &p.CreatedAt, &p.UpdatedAt,
				)
				if err != nil {
					return nil, err
				}

				// Set boat ID if valid (for regular events)
				if boatID.Valid {
					p.BoatID = boatID.Int64
				} else {
					p.BoatID = 0 // Default for entypsegling events
				}
			}

			// Set finish time if valid
			if finishTime.Valid {
				p.FinishTime = finishTime.String
			}

			// Set crew_count value
			if crewCount.Valid {
				p.CrewCount = int(crewCount.Int64)
			}

			// Set default values if NULL
			if srsType.Valid {
				p.SRSType = srsType.String
				log.Printf("DEBUG: GetParticipant - Participant %d has SRS type: %s", p.ID, p.SRSType)
			} else {
				p.SRSType = "srs"
				log.Printf("DEBUG: GetParticipant - Participant %d has NULL SRS type, defaulting to 'srs'", p.ID)
			}

			// Get the boat to set the correct SRS value based on SRS type
			boat, err := db.GetBoat(p.BoatID)
			if err == nil {
				// Determine which SRS value to use based on the SRS type
				var boatSRSValue float64
				switch p.SRSType {
				case "srs_utan_undanvindsegel":
					boatSRSValue = boat.SRSUtanUndanvindsegel
					log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
				case "srs_shorthanded":
					boatSRSValue = boat.SRSShorthanded
					log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_shorthanded', boat SRS value: %f", p.ID, boatSRSValue)
				case "srs_shorthanded_utan_undanvindsegel":
					boatSRSValue = boat.SRSShorthandedUtanUndanvindsegel
					log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_shorthanded_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
				default:
					boatSRSValue = boat.SRS
					log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs', boat SRS value: %f", p.ID, boatSRSValue)
				}

				// Always use the boat's SRS value based on the SRS type
				p.SelectedSRSValue = boatSRSValue
				log.Printf("DEBUG: GetParticipant - Participant %d using boat SRS value based on SRS type: %f", p.ID, p.SelectedSRSValue)
			} else if selectedSRSValue.Valid {
				// If we couldn't get the boat but have a valid selectedSRSValue, use it
				p.SelectedSRSValue = selectedSRSValue.Float64
				log.Printf("DEBUG: GetParticipant - Participant %d could not get boat, using stored selected SRS value: %f", p.ID, p.SelectedSRSValue)
			} else {
				log.Printf("DEBUG: GetParticipant - Participant %d has NULL selected SRS value and could not get boat SRS: %v", p.ID, err)
			}

			if customSRSValue.Valid {
				p.CustomSRSValue = customSRSValue.Float64
				log.Printf("DEBUG: GetParticipant - Participant %d has custom SRS value: %f", p.ID, p.CustomSRSValue)
			} else {
				log.Printf("DEBUG: GetParticipant - Participant %d has NULL custom SRS value", p.ID)
			}

			if useCustomSRSValue.Valid {
				p.UseCustomSRSValue = useCustomSRSValue.Bool
				log.Printf("DEBUG: GetParticipant - Participant %d uses custom SRS value: %v", p.ID, p.UseCustomSRSValue)
			} else {
				log.Printf("DEBUG: GetParticipant - Participant %d has NULL use_custom_srs_value", p.ID)
			}
		} else if count > 0 && hasFinishTime > 0 {
			// Use nullable types for the SRS fields and boat_id
			var srsType sql.NullString
			var selectedSRSValue, customSRSValue sql.NullFloat64
			var useCustomSRSValue sql.NullBool
			var finishTime sql.NullString
			var boatID sql.NullInt64

			err := rows.Scan(
				&p.ID, &p.EventID, &p.SailorID, &boatID,
				&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
				&finishTime, &p.CreatedAt, &p.UpdatedAt,
			)
			if err != nil {
				return nil, err
			}

			// Set boat ID if valid
			if boatID.Valid {
				p.BoatID = boatID.Int64
			} else {
				p.BoatID = 0
			}

			// Set finish time if valid
			if finishTime.Valid {
				p.FinishTime = finishTime.String
			}

			// Set default values if NULL
			if srsType.Valid {
				p.SRSType = srsType.String
				log.Printf("DEBUG: GetParticipant - Participant %d has SRS type: %s", p.ID, p.SRSType)
			} else {
				p.SRSType = "srs"
				log.Printf("DEBUG: GetParticipant - Participant %d has NULL SRS type, defaulting to 'srs'", p.ID)
			}

			// Get the boat to set the correct SRS value based on SRS type
			boat, err := db.GetBoat(p.BoatID)
			if err == nil {
				// Determine which SRS value to use based on the SRS type
				var boatSRSValue float64
				switch p.SRSType {
				case "srs_utan_undanvindsegel":
					boatSRSValue = boat.SRSUtanUndanvindsegel
					log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
				case "srs_shorthanded":
					boatSRSValue = boat.SRSShorthanded
					log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_shorthanded', boat SRS value: %f", p.ID, boatSRSValue)
				case "srs_shorthanded_utan_undanvindsegel":
					boatSRSValue = boat.SRSShorthandedUtanUndanvindsegel
					log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs_shorthanded_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
				default:
					boatSRSValue = boat.SRS
					log.Printf("DEBUG: GetParticipant - Participant %d SRS type is 'srs', boat SRS value: %f", p.ID, boatSRSValue)
				}

				// Always use the boat's SRS value based on the SRS type
				p.SelectedSRSValue = boatSRSValue
				log.Printf("DEBUG: GetParticipant - Participant %d using boat SRS value based on SRS type: %f", p.ID, p.SelectedSRSValue)
			} else if selectedSRSValue.Valid {
				// If we couldn't get the boat but have a valid selectedSRSValue, use it
				p.SelectedSRSValue = selectedSRSValue.Float64
				log.Printf("DEBUG: GetParticipant - Participant %d could not get boat, using stored selected SRS value: %f", p.ID, p.SelectedSRSValue)
			} else {
				log.Printf("DEBUG: GetParticipant - Participant %d has NULL selected SRS value and could not get boat SRS: %v", p.ID, err)
			}

			if customSRSValue.Valid {
				p.CustomSRSValue = customSRSValue.Float64
				log.Printf("DEBUG: GetParticipant - Participant %d has custom SRS value: %f", p.ID, p.CustomSRSValue)
			} else {
				log.Printf("DEBUG: GetParticipant - Participant %d has NULL custom SRS value", p.ID)
			}

			if useCustomSRSValue.Valid {
				p.UseCustomSRSValue = useCustomSRSValue.Bool
				log.Printf("DEBUG: GetParticipant - Participant %d uses custom SRS value: %v", p.ID, p.UseCustomSRSValue)
			} else {
				log.Printf("DEBUG: GetParticipant - Participant %d has NULL use_custom_srs_value", p.ID)
			}
		} else if count > 0 {
			// If only srs_type exists but not finish_time, use the intermediate schema
			// Use nullable types for the SRS fields and boat_id
			var srsType sql.NullString
			var selectedSRSValue, customSRSValue sql.NullFloat64
			var useCustomSRSValue sql.NullBool
			var boatID sql.NullInt64

			err := rows.Scan(
				&p.ID, &p.EventID, &p.SailorID, &boatID,
				&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
				&p.CreatedAt, &p.UpdatedAt,
			)
			if err != nil {
				return nil, err
			}

			// Set boat ID if valid
			if boatID.Valid {
				p.BoatID = boatID.Int64
			} else {
				p.BoatID = 0
			}

			// Set default values if NULL
			if srsType.Valid {
				p.SRSType = srsType.String
				log.Printf("Participant %d has SRS type: %s", p.ID, p.SRSType)
			} else {
				p.SRSType = "srs"
				log.Printf("Participant %d has NULL SRS type, defaulting to 'srs'", p.ID)
			}

			// Get the boat to set the correct SRS value based on SRS type (only if boat_id is valid)
			if p.BoatID > 0 {
				boat, err := db.GetBoat(p.BoatID)
				if err == nil {
					// Determine which SRS value to use based on the SRS type
					var boatSRSValue float64
					switch p.SRSType {
					case "srs_utan_undanvindsegel":
						boatSRSValue = boat.SRSUtanUndanvindsegel
						log.Printf("Participant %d SRS type is 'srs_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
					case "srs_shorthanded":
						boatSRSValue = boat.SRSShorthanded
						log.Printf("Participant %d SRS type is 'srs_shorthanded', boat SRS value: %f", p.ID, boatSRSValue)
					case "srs_shorthanded_utan_undanvindsegel":
						boatSRSValue = boat.SRSShorthandedUtanUndanvindsegel
						log.Printf("Participant %d SRS type is 'srs_shorthanded_utan_undanvindsegel', boat SRS value: %f", p.ID, boatSRSValue)
					default:
						boatSRSValue = boat.SRS
						log.Printf("Participant %d SRS type is 'srs', boat SRS value: %f", p.ID, boatSRSValue)
					}

					// Always use the boat's SRS value based on the SRS type
					p.SelectedSRSValue = boatSRSValue
					log.Printf("Participant %d using boat SRS value based on SRS type: %f", p.ID, p.SelectedSRSValue)
				} else if selectedSRSValue.Valid {
					// If we couldn't get the boat but have a valid selectedSRSValue, use it
					p.SelectedSRSValue = selectedSRSValue.Float64
					log.Printf("Participant %d could not get boat, using stored selected SRS value: %f", p.ID, p.SelectedSRSValue)
				} else {
					log.Printf("Participant %d has NULL selected SRS value and could not get boat SRS: %v", p.ID, err)
				}
			}

			if customSRSValue.Valid {
				p.CustomSRSValue = customSRSValue.Float64
				log.Printf("Participant %d has custom SRS value: %f", p.ID, p.CustomSRSValue)
			} else {
				log.Printf("Participant %d has NULL custom SRS value", p.ID)
			}

			if useCustomSRSValue.Valid {
				p.UseCustomSRSValue = useCustomSRSValue.Bool
				log.Printf("Participant %d uses custom SRS value: %v", p.ID, p.UseCustomSRSValue)
			} else {
				log.Printf("Participant %d has NULL use_custom_srs_value", p.ID)
			}
		} else {
			// Otherwise, scan only the old fields with nullable boat_id
			var boatID sql.NullInt64
			err := rows.Scan(
				&p.ID, &p.EventID, &p.SailorID, &boatID,
				&p.CreatedAt, &p.UpdatedAt,
			)
			if err != nil {
				return nil, err
			}

			// Set boat ID if valid
			if boatID.Valid {
				p.BoatID = boatID.Int64
				// Get the boat to set the default SRS value
				boat, err := db.GetBoat(p.BoatID)
				if err == nil {
					p.SelectedSRSValue = boat.SRS
				}
			} else {
				p.BoatID = 0
			}
		}

		participants = append(participants, p)
	}

	return participants, nil
}

// CopyParticipantsFromEvent copies participants from one event to another, skipping duplicates
func (db *DB) CopyParticipantsFromEvent(sourceEventID, targetEventID int64) (int, error) {
	// Get all participants from the source event
	sourceParticipants, err := db.GetEventParticipants(sourceEventID)
	if err != nil {
		return 0, err
	}

	// Get all participants from the target event to check for duplicates
	targetParticipants, err := db.GetEventParticipants(targetEventID)
	if err != nil {
		return 0, err
	}

	// Create a map of existing sailor+boat combinations in the target event
	existingCombinations := make(map[string]bool)
	for _, tp := range targetParticipants {
		key := fmt.Sprintf("%d-%d", tp.SailorID, tp.BoatID)
		existingCombinations[key] = true
	}

	// Copy each participant to the target event, skipping duplicates
	count := 0
	for _, p := range sourceParticipants {
		// Check if this sailor+boat combination already exists in the target event
		key := fmt.Sprintf("%d-%d", p.SailorID, p.BoatID)
		if existingCombinations[key] {
			// Skip this participant as it's already in the target event
			continue
		}

		// Add the participant to the target event
		participantID, err := db.AddParticipantToEvent(
			targetEventID, p.SailorID, p.BoatID,
			p.SRSType, p.SelectedSRSValue, p.CustomSRSValue, p.UseCustomSRSValue,
		)
		if err != nil {
			return count, err
		}

		// Update crew_count if needed
		if p.CrewCount > 0 {
			_, err = db.Exec(`
				UPDATE event_participants
				SET crew_count = ?
				WHERE id = ?
			`, p.CrewCount, participantID)

			if err != nil {
				log.Printf("Warning: Could not update crew count for copied participant %d: %v", participantID, err)
			}
		}
		count++
	}

	return count, nil
}

// SRS Data Management Functions

// SaveSRSBoatType saves a boat type to the srs_boat_types table
func (db *DB) SaveSRSBoatType(battyp string, srs, srsUU, srsSH, srsSHUU float64) error {
	now := time.Now()

	// Check if the boat type already exists
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM srs_boat_types WHERE battyp = ?", battyp).Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		// Update existing record
		_, err = db.Exec(`
			UPDATE srs_boat_types
			SET srs = ?, srs_utan_undanvindsegel = ?, srs_shorthanded = ?,
				srs_shorthanded_utan_undanvindsegel = ?, updated_at = ?
			WHERE battyp = ?
		`, srs, srsUU, srsSH, srsSHUU, now, battyp)
	} else {
		// Insert new record
		_, err = db.Exec(`
			INSERT INTO srs_boat_types
			(battyp, srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?)
		`, battyp, srs, srsUU, srsSH, srsSHUU, now, now)
	}

	return err
}

// SaveSRSBoatTypesBatch saves multiple boat types to the srs_boat_types table in a single transaction
func (db *DB) SaveSRSBoatTypesBatch(entries []models.SRSBoatType) error {
	// Begin a transaction
	tx, err := db.Begin()
	if err != nil {
		return err
	}

	// Track if the transaction has been committed
	committed := false
	defer func() {
		if !committed && tx != nil {
			_ = tx.Rollback()
		}
	}()

	// Prepare statements for insert and update
	insertStmt, err := tx.Prepare(`
		INSERT INTO srs_boat_types
		(battyp, srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`)
	if err != nil {
		return err
	}
	defer insertStmt.Close()

	updateStmt, err := tx.Prepare(`
		UPDATE srs_boat_types
		SET srs = ?, srs_utan_undanvindsegel = ?, srs_shorthanded = ?,
			srs_shorthanded_utan_undanvindsegel = ?, updated_at = ?
		WHERE battyp = ?
	`)
	if err != nil {
		return err
	}
	defer updateStmt.Close()

	// First, load all existing boat types into a map for quick lookup
	existingBoatTypes := make(map[string]bool)
	rows, err := tx.Query("SELECT battyp FROM srs_boat_types")
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var battyp string
		if err := rows.Scan(&battyp); err != nil {
			return err
		}
		existingBoatTypes[battyp] = true
	}

	// Process entries in batches
	now := time.Now()
	batchSize := 100
	for i := 0; i < len(entries); i += batchSize {
		end := i + batchSize
		if end > len(entries) {
			end = len(entries)
		}

		log.Printf("Processing boat type batch %d to %d of %d", i+1, end, len(entries))

		for _, entry := range entries[i:end] {
			if _, exists := existingBoatTypes[entry.Battyp]; exists {
				// Update existing record
				_, err = updateStmt.Exec(
					entry.SRS, entry.SRSUtanUndanvindsegel,
					entry.SRSShorthanded, entry.SRSShorthandedUtanUndanvindsegel,
					now, entry.Battyp,
				)
			} else {
				// Insert new record
				_, err = insertStmt.Exec(
					entry.Battyp, entry.SRS, entry.SRSUtanUndanvindsegel,
					entry.SRSShorthanded, entry.SRSShorthandedUtanUndanvindsegel,
					now, now,
				)
				// Add to map to track as existing for future entries
				existingBoatTypes[entry.Battyp] = true
			}

			if err != nil {
				log.Printf("Error saving boat type %s: %v", entry.Battyp, err)
				continue
			}
		}
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return err
	}
	committed = true

	return nil
}

// SaveSRSMatbrev saves a boat with measurement certificate to the srs_matbrev table
func (db *DB) SaveSRSMatbrev(matbrevsNummer, battyp, batNamn, agare, segelnummer, nationality string, srs, srsUU, srsSH, srsSHUU float64) error {
	now := time.Now()

	// Default nationality to SWE if empty
	if nationality == "" {
		nationality = "SWE"
	}

	// Check if the boat already exists
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM srs_matbrev WHERE matbrevs_nummer = ?", matbrevsNummer).Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		// Update existing record
		_, err = db.Exec(`
			UPDATE srs_matbrev
			SET battyp = ?, bat_namn = ?, agare = ?, segelnummer = ?, nationality = ?, srs = ?, srs_utan_undanvindsegel = ?,
				srs_shorthanded = ?, srs_shorthanded_utan_undanvindsegel = ?, updated_at = ?
			WHERE matbrevs_nummer = ?
		`, battyp, batNamn, agare, segelnummer, nationality, srs, srsUU, srsSH, srsSHUU, now, matbrevsNummer)
	} else {
		// Insert new record
		_, err = db.Exec(`
			INSERT INTO srs_matbrev
			(matbrevs_nummer, battyp, bat_namn, agare, segelnummer, nationality, srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		`, matbrevsNummer, battyp, batNamn, agare, segelnummer, nationality, srs, srsUU, srsSH, srsSHUU, now, now)
	}

	return err
}

// SaveSRSMatbrevBatch saves multiple boats with measurement certificates to the srs_matbrev table in a single transaction
func (db *DB) SaveSRSMatbrevBatch(entries []models.SRSMatbrev) error {
	// Begin a transaction
	tx, err := db.Begin()
	if err != nil {
		return err
	}

	// Track if the transaction has been committed
	committed := false
	defer func() {
		if !committed && tx != nil {
			_ = tx.Rollback()
		}
	}()

	// Prepare statements for insert and update
	insertStmt, err := tx.Prepare(`
		INSERT INTO srs_matbrev
		(matbrevs_nummer, battyp, bat_namn, agare, segelnummer, nationality, srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`)
	if err != nil {
		return err
	}
	defer insertStmt.Close()

	updateStmt, err := tx.Prepare(`
		UPDATE srs_matbrev
		SET battyp = ?, bat_namn = ?, agare = ?, segelnummer = ?, nationality = ?, srs = ?, srs_utan_undanvindsegel = ?,
			srs_shorthanded = ?, srs_shorthanded_utan_undanvindsegel = ?, updated_at = ?
		WHERE matbrevs_nummer = ?
	`)
	if err != nil {
		return err
	}
	defer updateStmt.Close()

	// First, load all existing matbrev numbers into a map for quick lookup
	existingMatbrev := make(map[string]bool)
	rows, err := tx.Query("SELECT matbrevs_nummer FROM srs_matbrev")
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var matbrevsNummer string
		if err := rows.Scan(&matbrevsNummer); err != nil {
			return err
		}
		existingMatbrev[matbrevsNummer] = true
	}

	// Process entries in batches
	now := time.Now()
	batchSize := 100
	for i := 0; i < len(entries); i += batchSize {
		end := i + batchSize
		if end > len(entries) {
			end = len(entries)
		}

		log.Printf("Processing matbrev batch %d to %d of %d", i+1, end, len(entries))

		for _, entry := range entries[i:end] {
			// Default nationality to SWE if empty
			nationality := entry.Nationality
			if nationality == "" {
				nationality = "SWE"
			}

			if _, exists := existingMatbrev[entry.MatbrevsNummer]; exists {
				// Update existing record
				_, err = updateStmt.Exec(
					entry.Battyp, entry.BatNamn, entry.Agare, entry.Segelnummer, nationality,
					entry.SRS, entry.SRSUtanUndanvindsegel, entry.SRSShorthanded, entry.SRSShorthandedUtanUndanvindsegel,
					now, entry.MatbrevsNummer,
				)
			} else {
				// Insert new record
				_, err = insertStmt.Exec(
					entry.MatbrevsNummer, entry.Battyp, entry.BatNamn, entry.Agare, entry.Segelnummer, nationality,
					entry.SRS, entry.SRSUtanUndanvindsegel, entry.SRSShorthanded, entry.SRSShorthandedUtanUndanvindsegel,
					now, now,
				)
				// Add to map to track as existing for future entries
				existingMatbrev[entry.MatbrevsNummer] = true
			}

			if err != nil {
				log.Printf("Error saving matbrev %s: %v", entry.MatbrevsNummer, err)
				continue
			}
		}
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return err
	}
	committed = true

	return nil
}

// LogSRSSync adds a log entry for SRS data synchronization
func (db *DB) LogSRSSync(source, status, message string) error {
	now := time.Now()
	_, err := db.Exec(`
		INSERT INTO srs_sync_log (source, status, message, created_at)
		VALUES (?, ?, ?, ?)
	`, source, status, message, now)

	return err
}

// GetSRSBoatTypes returns all boat types from the srs_boat_types table
func (db *DB) GetSRSBoatTypes() ([]models.SRSBoatType, error) {
	rows, err := db.Query(`
		SELECT id, battyp, srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM srs_boat_types
		ORDER BY battyp
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var boatTypes []models.SRSBoatType
	for rows.Next() {
		var bt models.SRSBoatType
		err := rows.Scan(&bt.ID, &bt.Battyp, &bt.SRS, &bt.SRSUtanUndanvindsegel,
			&bt.SRSShorthanded, &bt.SRSShorthandedUtanUndanvindsegel, &bt.CreatedAt, &bt.UpdatedAt)
		if err != nil {
			return nil, err
		}
		boatTypes = append(boatTypes, bt)
	}

	return boatTypes, nil
}

// GetSRSBoatTypesLimit returns a limited number of boat types from the srs_boat_types table
func (db *DB) GetSRSBoatTypesLimit(limit int) ([]models.SRSBoatType, error) {
	rows, err := db.Query(`
		SELECT id, battyp, srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM srs_boat_types
		ORDER BY battyp
		LIMIT ?
	`, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var boatTypes []models.SRSBoatType
	for rows.Next() {
		var bt models.SRSBoatType
		err := rows.Scan(&bt.ID, &bt.Battyp, &bt.SRS, &bt.SRSUtanUndanvindsegel,
			&bt.SRSShorthanded, &bt.SRSShorthandedUtanUndanvindsegel, &bt.CreatedAt, &bt.UpdatedAt)
		if err != nil {
			return nil, err
		}
		boatTypes = append(boatTypes, bt)
	}

	return boatTypes, nil
}

// SearchSRSBoatTypes searches for boat types by name
func (db *DB) SearchSRSBoatTypes(query string) ([]models.SRSBoatType, error) {
	searchQuery := "%" + query + "%"
	rows, err := db.Query(`
		SELECT id, battyp, srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM srs_boat_types
		WHERE battyp LIKE ?
		ORDER BY
			CASE WHEN battyp = ? THEN 0
				 WHEN battyp LIKE ? THEN 1
				 ELSE 2
			END,
			battyp
		LIMIT 100
	`, searchQuery, query, query+"%")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var boatTypes []models.SRSBoatType
	for rows.Next() {
		var bt models.SRSBoatType
		err := rows.Scan(&bt.ID, &bt.Battyp, &bt.SRS, &bt.SRSUtanUndanvindsegel,
			&bt.SRSShorthanded, &bt.SRSShorthandedUtanUndanvindsegel, &bt.CreatedAt, &bt.UpdatedAt)
		if err != nil {
			return nil, err
		}
		boatTypes = append(boatTypes, bt)
	}

	return boatTypes, nil
}

// GetSRSMatbrev returns all boats with measurement certificates from the srs_matbrev table
func (db *DB) GetSRSMatbrev() ([]models.SRSMatbrev, error) {
	rows, err := db.Query(`
		SELECT id, matbrevs_nummer, battyp, bat_namn, agare, segelnummer, nationality, srs, srs_utan_undanvindsegel,
			   srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM srs_matbrev
		ORDER BY battyp, agare
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var boats []models.SRSMatbrev
	for rows.Next() {
		var b models.SRSMatbrev
		var batNamn, segelnummer, nationality sql.NullString
		err := rows.Scan(&b.ID, &b.MatbrevsNummer, &b.Battyp, &batNamn, &b.Agare, &segelnummer, &nationality,
			&b.SRS, &b.SRSUtanUndanvindsegel, &b.SRSShorthanded, &b.SRSShorthandedUtanUndanvindsegel,
			&b.CreatedAt, &b.UpdatedAt)
		if err != nil {
			return nil, err
		}
		// Convert NullString to string
		if batNamn.Valid {
			b.BatNamn = batNamn.String
		} else {
			b.BatNamn = "" // Empty string for NULL values
		}

		// Convert segelnummer NullString to string
		if segelnummer.Valid {
			b.Segelnummer = segelnummer.String
		} else {
			b.Segelnummer = "" // Empty string for NULL values
		}

		// Convert nationality NullString to string with default value
		if nationality.Valid {
			b.Nationality = nationality.String
		} else {
			b.Nationality = "SWE" // Default to SWE
		}

		boats = append(boats, b)
	}

	return boats, nil
}

// GetSRSMatbrevLimit returns a limited number of boats with measurement certificates from the srs_matbrev table
func (db *DB) GetSRSMatbrevLimit(limit int) ([]models.SRSMatbrev, error) {
	rows, err := db.Query(`
		SELECT id, matbrevs_nummer, battyp, bat_namn, agare, segelnummer, nationality, srs, srs_utan_undanvindsegel,
			   srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM srs_matbrev
		ORDER BY battyp, agare
		LIMIT ?
	`, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var boats []models.SRSMatbrev
	for rows.Next() {
		var b models.SRSMatbrev
		var batNamn, segelnummer, nationality sql.NullString
		err := rows.Scan(&b.ID, &b.MatbrevsNummer, &b.Battyp, &batNamn, &b.Agare, &segelnummer, &nationality,
			&b.SRS, &b.SRSUtanUndanvindsegel, &b.SRSShorthanded, &b.SRSShorthandedUtanUndanvindsegel,
			&b.CreatedAt, &b.UpdatedAt)
		if err != nil {
			return nil, err
		}
		// Convert NullString to string
		if batNamn.Valid {
			b.BatNamn = batNamn.String
		} else {
			b.BatNamn = "" // Empty string for NULL values
		}

		// Convert segelnummer NullString to string
		if segelnummer.Valid {
			b.Segelnummer = segelnummer.String
		} else {
			b.Segelnummer = "" // Empty string for NULL values
		}

		// Convert nationality NullString to string with default value
		if nationality.Valid {
			b.Nationality = nationality.String
		} else {
			b.Nationality = "SWE" // Default to SWE
		}

		boats = append(boats, b)
	}

	return boats, nil
}

// SearchSRSMatbrev searches for boats with measurement certificates by number, type, boat name, owner, or sail number
func (db *DB) SearchSRSMatbrev(query string) ([]models.SRSMatbrev, error) {
	searchQuery := "%" + query + "%"
	rows, err := db.Query(`
		SELECT id, matbrevs_nummer, battyp, bat_namn, agare, segelnummer, nationality, srs, srs_utan_undanvindsegel,
			   srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM srs_matbrev
		WHERE matbrevs_nummer LIKE ? OR battyp LIKE ? OR bat_namn LIKE ? OR agare LIKE ? OR segelnummer LIKE ?
		ORDER BY
			CASE
				WHEN matbrevs_nummer = ? THEN 0
				WHEN matbrevs_nummer LIKE ? THEN 1
				WHEN segelnummer = ? THEN 2
				WHEN segelnummer LIKE ? THEN 3
				WHEN battyp = ? THEN 4
				WHEN battyp LIKE ? THEN 5
				WHEN bat_namn = ? THEN 6
				WHEN bat_namn LIKE ? THEN 7
				WHEN agare = ? THEN 8
				WHEN agare LIKE ? THEN 9
				ELSE 10
			END,
			battyp, agare
		LIMIT 100
	`, searchQuery, searchQuery, searchQuery, searchQuery, searchQuery,
		query, query+"%",
		query, query+"%",
		query, query+"%",
		query, query+"%",
		query, query+"%")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var boats []models.SRSMatbrev
	for rows.Next() {
		var b models.SRSMatbrev
		var batNamn, segelnummer, nationality sql.NullString
		err := rows.Scan(&b.ID, &b.MatbrevsNummer, &b.Battyp, &batNamn, &b.Agare, &segelnummer, &nationality,
			&b.SRS, &b.SRSUtanUndanvindsegel, &b.SRSShorthanded, &b.SRSShorthandedUtanUndanvindsegel,
			&b.CreatedAt, &b.UpdatedAt)
		if err != nil {
			return nil, err
		}
		// Convert NullString to string
		if batNamn.Valid {
			b.BatNamn = batNamn.String
		} else {
			b.BatNamn = "" // Empty string for NULL values
		}

		// Convert segelnummer NullString to string
		if segelnummer.Valid {
			b.Segelnummer = segelnummer.String
		} else {
			b.Segelnummer = "" // Empty string for NULL values
		}

		// Convert nationality NullString to string with default value
		if nationality.Valid {
			b.Nationality = nationality.String
		} else {
			b.Nationality = "SWE" // Default to SWE
		}

		boats = append(boats, b)
	}

	return boats, nil
}

// GetSRSSyncLogs returns the sync logs from the srs_sync_log table
func (db *DB) GetSRSSyncLogs(limit int) ([]models.SRSSyncLog, error) {
	rows, err := db.Query(`
		SELECT id, source, status, message, created_at
		FROM srs_sync_log
		ORDER BY created_at DESC
		LIMIT ?
	`, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []models.SRSSyncLog
	for rows.Next() {
		var log models.SRSSyncLog
		err := rows.Scan(&log.ID, &log.Source, &log.Status, &log.Message, &log.CreatedAt)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	return logs, nil
}

// GetSRSBoatTypeByName returns a specific boat type from the srs_boat_types table
func (db *DB) GetSRSBoatTypeByName(battyp string) (models.SRSBoatType, error) {
	var bt models.SRSBoatType
	err := db.QueryRow(`
		SELECT id, battyp, srs, srs_utan_undanvindsegel, srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM srs_boat_types
		WHERE battyp = ?
	`, battyp).Scan(&bt.ID, &bt.Battyp, &bt.SRS, &bt.SRSUtanUndanvindsegel,
		&bt.SRSShorthanded, &bt.SRSShorthandedUtanUndanvindsegel, &bt.CreatedAt, &bt.UpdatedAt)

	return bt, err
}

// GetSRSBoatTypeNames returns all boat type names from the srs_boat_types table
func (db *DB) GetSRSBoatTypeNames() ([]string, error) {
	rows, err := db.Query(`
		SELECT battyp
		FROM srs_boat_types
		ORDER BY battyp
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var boatTypes []string
	for rows.Next() {
		var battyp string
		err := rows.Scan(&battyp)
		if err != nil {
			return nil, err
		}
		boatTypes = append(boatTypes, battyp)
	}

	return boatTypes, nil
}

// GetSRSMatbrevByNumber returns a specific boat with measurement certificate from the srs_matbrev table
func (db *DB) GetSRSMatbrevByNumber(matbrevsNummer string) (models.SRSMatbrev, error) {
	var b models.SRSMatbrev
	var batNamn, segelnummer, nationality sql.NullString

	// Normalize the mätbrevsnummer by trimming whitespace and converting to uppercase
	normalizedMatbrevsNummer := strings.TrimSpace(strings.ToUpper(matbrevsNummer))

	// Use UPPER() in the SQL query to make it case-insensitive
	err := db.QueryRow(`
		SELECT id, matbrevs_nummer, battyp, bat_namn, agare, segelnummer, nationality, srs, srs_utan_undanvindsegel,
			   srs_shorthanded, srs_shorthanded_utan_undanvindsegel, created_at, updated_at
		FROM srs_matbrev
		WHERE UPPER(matbrevs_nummer) = ?
	`, normalizedMatbrevsNummer).Scan(&b.ID, &b.MatbrevsNummer, &b.Battyp, &batNamn, &b.Agare, &segelnummer, &nationality,
		&b.SRS, &b.SRSUtanUndanvindsegel, &b.SRSShorthanded, &b.SRSShorthandedUtanUndanvindsegel,
		&b.CreatedAt, &b.UpdatedAt)

	// Convert NullString to string
	if batNamn.Valid {
		b.BatNamn = batNamn.String
	} else {
		b.BatNamn = "" // Empty string for NULL values
	}

	// Convert segelnummer NullString to string
	if segelnummer.Valid {
		b.Segelnummer = segelnummer.String
	} else {
		b.Segelnummer = "" // Empty string for NULL values
	}

	// Convert nationality NullString to string with default value
	if nationality.Valid {
		b.Nationality = nationality.String
	} else {
		b.Nationality = "SWE" // Default to SWE
	}

	return b, err
}

// ClearSRSBoatTypes removes all entries from the SRS table
func (db *DB) ClearSRSBoatTypes() error {
	// Use a transaction for better performance
	tx, err := db.Begin()
	if err != nil {
		return err
	}

	// Defer rollback in case of error
	defer func() {
		if tx != nil {
			_ = tx.Rollback()
		}
	}()

	// Delete all records
	_, err = tx.Exec("DELETE FROM srs_boat_types")
	if err != nil {
		return err
	}

	// Commit the transaction
	err = tx.Commit()
	tx = nil
	return err
}

// ClearSRSMatbrev removes all boats with measurement certificates from the srs_matbrev table
func (db *DB) ClearSRSMatbrev() error {
	// Use a transaction for better performance
	tx, err := db.Begin()
	if err != nil {
		return err
	}

	// Defer rollback in case of error
	defer func() {
		if tx != nil {
			_ = tx.Rollback()
		}
	}()

	// Delete all records
	_, err = tx.Exec("DELETE FROM srs_matbrev")
	if err != nil {
		return err
	}

	// Commit the transaction
	err = tx.Commit()
	tx = nil
	return err
}

// initializeDefaultSettings sets up default settings if they don't exist
func (db *DB) initializeDefaultSettings() error {
	now := time.Now()

	// Default settings
	settings := map[string]string{
		"auto_backup_on_event_create":    "true",                             // Enable automatic backups by default
		"default_club":                   "LSS",                              // Default club for new sailors
		"use_24h_time":                   "true",                             // Use 24-hour time format by default
		"github_pages_enabled":           "false",                            // GitHub Pages integration disabled by default
		"github_pages_repo":              "",                                 // GitHub repository URL
		"github_pages_branch":            "gh-pages",                         // Default branch for GitHub Pages
		"github_pages_token":             "",                                 // GitHub personal access token
		"weather_api_token":              "44b7a85536ef4238bddd7d885976a1f0", // Trafikverket weather API token
		"weather_location":               "Linköping",                        // Default weather station location
		"github_pages_template":          "default",                          // Template to use for GitHub Pages
		"competition_types":              "Kvällssegling,Regatta",            // Default competition types
		"default_competition_type":       "Kvällssegling",                    // Default competition type for new events
		"google_drive_enabled":           "false",                            // Google Drive integration disabled by default
		"google_drive_folder_id":         "",                                 // Google Drive folder ID for exports
		"google_drive_naming_convention": "{{event_name}}_{{date}}",          // Default naming convention for exported files
	}

	// Insert default settings if they don't exist
	for key, value := range settings {
		var count int
		err := db.QueryRow("SELECT COUNT(*) FROM settings WHERE key = ?", key).Scan(&count)
		if err != nil {
			return err
		}

		if count == 0 {
			_, err = db.Exec(`
				INSERT INTO settings (key, value, created_at, updated_at)
				VALUES (?, ?, ?, ?)
			`, key, value, now, now)

			if err != nil {
				return err
			}

			log.Printf("Initialized default setting: %s = %s", key, value)
		}
	}

	return nil
}

// migrateGitHubToken migrates the GitHub token from the database to a file
func (db *DB) migrateGitHubToken() error {
	// Check if token already exists in file
	fileToken, err := utils.GetGitHubToken()
	if err != nil {
		return fmt.Errorf("failed to check if GitHub token exists in file: %w", err)
	}

	// If token already exists in file, no need to migrate
	if fileToken != "" {
		log.Printf("GitHub token already exists in file, no need to migrate")
		return nil
	}

	// Get token from database
	var dbToken string
	err = db.QueryRow("SELECT value FROM settings WHERE key = 'github_pages_token'").Scan(&dbToken)
	if err != nil {
		if err == sql.ErrNoRows {
			// No token in database, nothing to migrate
			log.Printf("No GitHub token found in database, nothing to migrate")
			return nil
		}
		return fmt.Errorf("failed to get GitHub token from database: %w", err)
	}

	// If token is empty or already a placeholder, nothing to migrate
	if dbToken == "" || dbToken == "********" {
		log.Printf("GitHub token in database is empty or already a placeholder, nothing to migrate")
		return nil
	}

	// Save token to file
	if err := utils.SaveGitHubToken(dbToken); err != nil {
		return fmt.Errorf("failed to save GitHub token to file: %w", err)
	}

	// Replace token in database with placeholder
	_, err = db.Exec("UPDATE settings SET value = '********' WHERE key = 'github_pages_token'")
	if err != nil {
		log.Printf("Warning: Failed to replace GitHub token in database with placeholder: %v", err)
		// Continue even if this fails, as the token is now safely stored in the file
	} else {
		log.Printf("GitHub token in database replaced with placeholder")
	}

	log.Printf("GitHub token successfully migrated from database to file")
	return nil
}

// GetSetting retrieves a setting value by key
func (db *DB) GetSetting(key string) (string, error) {
	// Special case for GitHub token - read from file instead of database
	if key == "github_pages_token" {
		token, err := utils.GetGitHubToken()
		if err != nil {
			log.Printf("Error reading GitHub token from file: %v", err)
			// Fall back to database if file read fails
		} else if token != "" {
			return token, nil
		}
		// If token is empty or file read failed, fall back to database
	}

	var value string
	err := db.QueryRow("SELECT value FROM settings WHERE key = ?", key).Scan(&value)
	return value, err
}

// SetSetting updates a setting value
func (db *DB) SetSetting(key, value string) error {
	// Special case for GitHub token - save to file instead of database
	if key == "github_pages_token" {
		if err := utils.SaveGitHubToken(value); err != nil {
			return fmt.Errorf("failed to save GitHub token to file: %w", err)
		}
		// Still store a placeholder in the database to indicate token exists
		// but don't store the actual token
		if value != "" {
			value = "********" // Replace with placeholder
		}
	}
	now := time.Now()

	// Check if the setting exists
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM settings WHERE key = ?", key).Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		// Update existing setting
		_, err = db.Exec(`
			UPDATE settings
			SET value = ?, updated_at = ?
			WHERE key = ?
		`, value, now, key)
	} else {
		// Insert new setting
		_, err = db.Exec(`
			INSERT INTO settings (key, value, created_at, updated_at)
			VALUES (?, ?, ?, ?)
		`, key, value, now, now)
	}

	return err
}

// CreateDatabaseBackup creates a backup of the database file
func (db *DB) CreateDatabaseBackup(dbPath string) (string, error) {
	// Get the database file path
	if dbPath == "" {
		return "", fmt.Errorf("database path is empty")
	}

	log.Printf("DEBUG: CreateDatabaseBackup - Creating backup of database at path: %s", dbPath)

	// Create backups directory if it doesn't exist
	backupDir := filepath.Join(filepath.Dir(dbPath), "backups")
	log.Printf("DEBUG: CreateDatabaseBackup - Backup directory: %s", backupDir)

	err := os.MkdirAll(backupDir, 0755)
	if err != nil {
		log.Printf("DEBUG: CreateDatabaseBackup - Failed to create backups directory: %v", err)
		return "", fmt.Errorf("failed to create backups directory: %w", err)
	}

	// Generate backup filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	backupFilename := fmt.Sprintf("%s_%s%s",
		strings.TrimSuffix(filepath.Base(dbPath), filepath.Ext(dbPath)),
		timestamp,
		filepath.Ext(dbPath))
	backupPath := filepath.Join(backupDir, backupFilename)

	log.Printf("DEBUG: CreateDatabaseBackup - Backup path: %s", backupPath)

	// Open source file
	source, err := os.Open(dbPath)
	if err != nil {
		log.Printf("DEBUG: CreateDatabaseBackup - Failed to open source database file: %v", err)
		return "", fmt.Errorf("failed to open source database file: %w", err)
	}
	defer source.Close()

	// Create destination file
	destination, err := os.Create(backupPath)
	if err != nil {
		log.Printf("DEBUG: CreateDatabaseBackup - Failed to create backup file: %v", err)
		return "", fmt.Errorf("failed to create backup file: %w", err)
	}
	defer destination.Close()

	// Copy the file
	bytesWritten, err := io.Copy(destination, source)
	if err != nil {
		log.Printf("DEBUG: CreateDatabaseBackup - Failed to copy database to backup: %v", err)
		return "", fmt.Errorf("failed to copy database to backup: %w", err)
	}

	log.Printf("DEBUG: CreateDatabaseBackup - Copied %d bytes to backup file", bytesWritten)
	log.Printf("Created database backup: %s", backupPath)
	return backupPath, nil
}

// CheckpointWAL performs a WAL checkpoint to sync changes to the main database file
func (db *DB) CheckpointWAL() error {
	log.Printf("DEBUG: CheckpointWAL - Performing WAL checkpoint")

	_, err := db.Exec("PRAGMA wal_checkpoint(FULL)")
	if err != nil {
		log.Printf("ERROR: CheckpointWAL - Failed to perform WAL checkpoint: %v", err)
		return fmt.Errorf("failed to perform WAL checkpoint: %w", err)
	}

	log.Printf("DEBUG: CheckpointWAL - WAL checkpoint completed successfully")
	return nil
}

// ensureEntypSeglingColumns adds entypsegling-related columns to the database if they don't exist
func (db *DB) ensureEntypSeglingColumns() error {
	// Check if entypsegling column exists in events table
	var hasEntypsegling int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'entypsegling'
	`).Scan(&hasEntypsegling)
	if err != nil {
		return err
	}

	// Add entypsegling column if it doesn't exist
	if hasEntypsegling == 0 {
		log.Println("Adding entypsegling column to events table...")
		_, err = db.Exec(`ALTER TABLE events ADD COLUMN entypsegling BOOLEAN DEFAULT 0`)
		if err != nil {
			return fmt.Errorf("failed to add entypsegling column: %w", err)
		}
		log.Println("entypsegling column added successfully")
	}

	// Check if boat_type column exists in events table
	var hasBoatType int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'boat_type'
	`).Scan(&hasBoatType)
	if err != nil {
		return err
	}

	// Add boat_type column if it doesn't exist
	if hasBoatType == 0 {
		log.Println("Adding boat_type column to events table...")
		_, err = db.Exec(`ALTER TABLE events ADD COLUMN boat_type TEXT DEFAULT ''`)
		if err != nil {
			return fmt.Errorf("failed to add boat_type column: %w", err)
		}
		log.Println("boat_type column added successfully")
	}

	// Check if jaktstart_type column exists in events table
	var hasJaktstartType int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'jaktstart_type'
	`).Scan(&hasJaktstartType)
	if err != nil {
		return err
	}

	// Add jaktstart_type column if it doesn't exist
	if hasJaktstartType == 0 {
		log.Println("Adding jaktstart_type column to events table...")
		_, err = db.Exec(`ALTER TABLE events ADD COLUMN jaktstart_type TEXT DEFAULT 'none'`)
		if err != nil {
			return fmt.Errorf("failed to add jaktstart_type column: %w", err)
		}
		log.Println("jaktstart_type column added successfully")

		// Migrate existing data: set jaktstart_type based on existing jaktstart and entypsegling values
		log.Println("Migrating existing events to use jaktstart_type...")
		_, err = db.Exec(`
			UPDATE events
			SET jaktstart_type = CASE
				WHEN entypsegling = 1 THEN 'entypsegling'
				WHEN jaktstart = 1 THEN 'regular'
				ELSE 'none'
			END
		`)
		if err != nil {
			return fmt.Errorf("failed to migrate existing events to jaktstart_type: %w", err)
		}
		log.Println("Existing events migrated to jaktstart_type successfully")
	}

	// Check if personal_number column exists in event_participants table
	var hasPersonalNumber int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('event_participants')
		WHERE name = 'personal_number'
	`).Scan(&hasPersonalNumber)
	if err != nil {
		return err
	}

	// Add personal_number column if it doesn't exist
	if hasPersonalNumber == 0 {
		log.Println("Adding personal_number column to event_participants table...")
		_, err = db.Exec(`ALTER TABLE event_participants ADD COLUMN personal_number TEXT DEFAULT ''`)
		if err != nil {
			return fmt.Errorf("failed to add personal_number column: %w", err)
		}
		log.Println("personal_number column added successfully")
	}

	// Check if boat_id is nullable for entypsegling support
	var tableSchema string
	err = db.QueryRow(`
		SELECT sql FROM sqlite_master
		WHERE type='table' AND name='event_participants'
	`).Scan(&tableSchema)
	if err != nil {
		return fmt.Errorf("failed to get table schema: %w", err)
	}

	// Check if boat_id is NOT NULL (needs to be made nullable)
	if strings.Contains(tableSchema, "boat_id INTEGER NOT NULL") {
		log.Println("Making boat_id nullable in event_participants table for entypsegling support...")

		// Temporarily disable foreign key constraints
		_, err = db.Exec(`PRAGMA foreign_keys = OFF`)
		if err != nil {
			return fmt.Errorf("failed to disable foreign keys: %w", err)
		}

		// Drop the new table if it exists from a previous failed migration
		_, err = db.Exec(`DROP TABLE IF EXISTS event_participants_new`)
		if err != nil {
			return fmt.Errorf("failed to drop existing event_participants_new table: %w", err)
		}

		// Create new table with nullable boat_id (without foreign key constraints for now)
		_, err = db.Exec(`
			CREATE TABLE event_participants_new (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				event_id INTEGER NOT NULL,
				sailor_id INTEGER NOT NULL,
				boat_id INTEGER, -- Made nullable for entypsegling events
				personal_number TEXT DEFAULT '',
				srs_type TEXT DEFAULT 'srs',
				selected_srs_value REAL DEFAULT 0,
				custom_srs_value REAL DEFAULT 0,
				use_custom_srs_value BOOLEAN DEFAULT 0,
				finish_time TEXT DEFAULT '',
				crew_count INTEGER DEFAULT 0,
				dns BOOLEAN DEFAULT 0,
				dnf BOOLEAN DEFAULT 0,
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
				updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
			)
		`)
		if err != nil {
			return fmt.Errorf("failed to create new event_participants table: %w", err)
		}

		// Copy data from old table to new table, preserving all existing data
		// First check which columns exist in the old table to avoid copying non-existent columns
		var hasSRSType, hasSelectedSRS, hasCustomSRS, hasUseCustomSRS, hasFinishTime, hasCrewCountCol, hasDNS, hasDNF, hasPersonalNumberCol int

		db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'srs_type'`).Scan(&hasSRSType)
		db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'selected_srs_value'`).Scan(&hasSelectedSRS)
		db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'custom_srs_value'`).Scan(&hasCustomSRS)
		db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'use_custom_srs_value'`).Scan(&hasUseCustomSRS)
		db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'finish_time'`).Scan(&hasFinishTime)
		db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'crew_count'`).Scan(&hasCrewCountCol)
		db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'dns'`).Scan(&hasDNS)
		db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'dnf'`).Scan(&hasDNF)
		db.QueryRow(`SELECT COUNT(*) FROM pragma_table_info('event_participants') WHERE name = 'personal_number'`).Scan(&hasPersonalNumberCol)

		// Build the INSERT query dynamically based on available columns
		insertCols := []string{"id", "event_id", "sailor_id", "boat_id", "created_at", "updated_at"}
		selectCols := []string{"id", "event_id", "sailor_id", "boat_id", "created_at", "updated_at"}

		if hasPersonalNumberCol > 0 {
			insertCols = append(insertCols, "personal_number")
			selectCols = append(selectCols, "personal_number")
		}
		if hasSRSType > 0 {
			insertCols = append(insertCols, "srs_type")
			selectCols = append(selectCols, "srs_type")
		}
		if hasSelectedSRS > 0 {
			insertCols = append(insertCols, "selected_srs_value")
			selectCols = append(selectCols, "selected_srs_value")
		}
		if hasCustomSRS > 0 {
			insertCols = append(insertCols, "custom_srs_value")
			selectCols = append(selectCols, "custom_srs_value")
		}
		if hasUseCustomSRS > 0 {
			insertCols = append(insertCols, "use_custom_srs_value")
			selectCols = append(selectCols, "use_custom_srs_value")
		}
		if hasFinishTime > 0 {
			insertCols = append(insertCols, "finish_time")
			selectCols = append(selectCols, "finish_time")
		}
		if hasCrewCountCol > 0 {
			insertCols = append(insertCols, "crew_count")
			selectCols = append(selectCols, "crew_count")
		}
		if hasDNS > 0 {
			insertCols = append(insertCols, "dns")
			selectCols = append(selectCols, "dns")
		}
		if hasDNF > 0 {
			insertCols = append(insertCols, "dnf")
			selectCols = append(selectCols, "dnf")
		}

		insertColsStr := strings.Join(insertCols, ", ")
		selectColsStr := strings.Join(selectCols, ", ")

		copyQuery := fmt.Sprintf(`
			INSERT INTO event_participants_new (%s)
			SELECT %s FROM event_participants
		`, insertColsStr, selectColsStr)

		log.Printf("Copying data with query: %s", copyQuery)
		_, err = db.Exec(copyQuery)
		if err != nil {
			return fmt.Errorf("failed to copy data to new event_participants table: %w", err)
		}

		// Drop old table and rename new table
		_, err = db.Exec(`DROP TABLE event_participants`)
		if err != nil {
			return fmt.Errorf("failed to drop old event_participants table: %w", err)
		}

		_, err = db.Exec(`ALTER TABLE event_participants_new RENAME TO event_participants`)
		if err != nil {
			return fmt.Errorf("failed to rename new event_participants table: %w", err)
		}

		// Re-enable foreign key constraints
		_, err = db.Exec(`PRAGMA foreign_keys = ON`)
		if err != nil {
			return fmt.Errorf("failed to re-enable foreign keys: %w", err)
		}

		log.Println("Successfully made boat_id nullable in event_participants table")
	}

	// Check if placement column exists in heat_finish_times table
	var hasPlacement int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('heat_finish_times')
		WHERE name = 'placement'
	`).Scan(&hasPlacement)
	if err != nil {
		return err
	}

	// Add placement column if it doesn't exist
	if hasPlacement == 0 {
		log.Println("Adding placement column to heat_finish_times table...")
		_, err = db.Exec(`ALTER TABLE heat_finish_times ADD COLUMN placement INTEGER DEFAULT 0`)
		if err != nil {
			return fmt.Errorf("failed to add placement column: %w", err)
		}
		log.Println("placement column added successfully")
	}

	// Check if discard_after_heats column exists in events table
	var hasDiscardAfterHeats int
	err = db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'discard_after_heats'
	`).Scan(&hasDiscardAfterHeats)
	if err != nil {
		return err
	}

	// Add discard_after_heats column if it doesn't exist
	if hasDiscardAfterHeats == 0 {
		log.Println("Adding discard_after_heats column to events table...")
		_, err = db.Exec(`ALTER TABLE events ADD COLUMN discard_after_heats INTEGER DEFAULT 0`)
		if err != nil {
			return fmt.Errorf("failed to add discard_after_heats column: %w", err)
		}
		log.Println("discard_after_heats column added successfully")
	}

	return nil
}
