package database

import (
	"fmt"
	"log"
	"sort"
	"time"

	"github.com/rb<PERSON><PERSON>gren/segling/pkg/models"
)

// CreateHeat creates a new heat for an event
func (db *DB) CreateHeat(eventID int64, heatNumber int, name, startTime string) (int64, error) {
	now := time.Now()

	result, err := db.Exec(`
		INSERT INTO heats (event_id, heat_number, name, start_time, created_at)
		VALUES (?, ?, ?, ?, ?)
	`, eventID, heatNumber, name, startTime, now)

	if err != nil {
		return 0, err
	}

	return result.LastInsertId()
}

// GetEventHeats returns all heats for an event
func (db *DB) GetEventHeats(eventID int64) ([]models.Heat, error) {
	rows, err := db.Query(`
		SELECT id, event_id, heat_number, name, start_time, created_at
		FROM heats
		WHERE event_id = ?
		ORDER BY heat_number
	`, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var heats []models.Heat
	for rows.Next() {
		var h models.Heat
		err := rows.Scan(&h.ID, &h.EventID, &h.HeatNumber, &h.Name, &h.StartTime, &h.CreatedAt)
		if err != nil {
			return nil, err
		}
		heats = append(heats, h)
	}

	return heats, nil
}

// GetHeat returns a specific heat by ID
func (db *DB) GetHeat(heatID int64) (models.Heat, error) {
	var h models.Heat
	err := db.QueryRow(`
		SELECT id, event_id, heat_number, name, start_time, created_at
		FROM heats
		WHERE id = ?
	`, heatID).Scan(&h.ID, &h.EventID, &h.HeatNumber, &h.Name, &h.StartTime, &h.CreatedAt)

	return h, err
}

// EnsureDefaultHeat ensures that an event has at least one heat (default heat)
func (db *DB) EnsureDefaultHeat(eventID int64) error {
	// Check if the event already has heats
	var count int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM heats WHERE event_id = ?
	`, eventID).Scan(&count)
	if err != nil {
		return err
	}

	// If no heats exist, create a default heat
	if count == 0 {
		// Get the event to check if it's entypsegling
		var entypsegling bool
		var eventStartTime string
		err = db.QueryRow(`SELECT starttid, entypsegling FROM events WHERE id = ?`, eventID).Scan(&eventStartTime, &entypsegling)
		if err != nil {
			eventStartTime = "10:00" // Default fallback time
			entypsegling = false
		}

		// For entypsegling events, don't use start time
		if entypsegling {
			eventStartTime = ""
		} else if eventStartTime == "" {
			eventStartTime = "10:00" // Default fallback time for regular events
		}

		_, err = db.CreateHeat(eventID, 1, "Heat 1", eventStartTime)
		if err != nil {
			return fmt.Errorf("failed to create default heat: %w", err)
		}

		if entypsegling {
			log.Printf("Created default heat for entypsegling event %d (no start time)", eventID)
		} else {
			log.Printf("Created default heat for event %d with start time %s", eventID, eventStartTime)
		}
	}

	return nil
}

// UpdateHeatFinishTime updates the finish time for a participant in a specific heat
func (db *DB) UpdateHeatFinishTime(heatID, participantID int64, finishTime string, dns, dnf bool) error {
	now := time.Now()

	// TEMPORARILY DISABLED: Check if the event is locked
	// var locked bool
	// err := db.QueryRow(`
	// 	SELECT e.locked
	// 	FROM events e
	// 	JOIN heats h ON e.id = h.event_id
	// 	WHERE h.id = ?
	// `, heatID).Scan(&locked)
	// if err != nil {
	// 	return fmt.Errorf("failed to check if event is locked: %w", err)
	// }

	// if locked {
	// 	return fmt.Errorf("tävlingen är låst och måltider kan inte uppdateras - lås upp tävlingen först")
	// }

	// Check if the finish time has seconds
	// If it's in HH:MM format (without seconds), append ":00" for seconds
	if len(finishTime) == 5 && finishTime != "" {
		finishTime = finishTime + ":00"
	}

	// Insert or update the heat finish time
	_, err := db.Exec(`
		INSERT INTO heat_finish_times (heat_id, participant_id, finish_time, dns, dnf, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?)
		ON CONFLICT(heat_id, participant_id) DO UPDATE SET
			finish_time = excluded.finish_time,
			dns = excluded.dns,
			dnf = excluded.dnf,
			updated_at = excluded.updated_at
	`, heatID, participantID, finishTime, dns, dnf, now, now)

	return err
}

// UpdateHeatPlacement updates the placement for a participant in a specific heat (for entypsegling events)
func (db *DB) UpdateHeatPlacement(heatID, participantID int64, placement int, dns, dnf bool) error {
	now := time.Now()

	// Insert or update the heat finish time with placement
	_, err := db.Exec(`
		INSERT INTO heat_finish_times (heat_id, participant_id, placement, dns, dnf, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?)
		ON CONFLICT(heat_id, participant_id) DO UPDATE SET
			placement = excluded.placement,
			dns = excluded.dns,
			dnf = excluded.dnf,
			updated_at = excluded.updated_at
	`, heatID, participantID, placement, dns, dnf, now, now)

	return err
}

// GetHeatFinishTimes returns all finish times for a specific heat
func (db *DB) GetHeatFinishTimes(heatID int64) ([]models.HeatFinishTime, error) {
	rows, err := db.Query(`
		SELECT id, heat_id, participant_id, finish_time, placement, dns, dnf, created_at, updated_at
		FROM heat_finish_times
		WHERE heat_id = ?
		ORDER BY participant_id
	`, heatID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var finishTimes []models.HeatFinishTime
	for rows.Next() {
		var ft models.HeatFinishTime
		err := rows.Scan(&ft.ID, &ft.HeatID, &ft.ParticipantID, &ft.FinishTime, &ft.Placement, &ft.DNS, &ft.DNF, &ft.CreatedAt, &ft.UpdatedAt)
		if err != nil {
			return nil, err
		}
		finishTimes = append(finishTimes, ft)
	}

	return finishTimes, nil
}

// GetParticipantHeatFinishTime returns the finish time for a specific participant in a specific heat
func (db *DB) GetParticipantHeatFinishTime(heatID, participantID int64) (models.HeatFinishTime, error) {
	var ft models.HeatFinishTime
	err := db.QueryRow(`
		SELECT id, heat_id, participant_id, finish_time, dns, dnf, created_at, updated_at
		FROM heat_finish_times
		WHERE heat_id = ? AND participant_id = ?
	`, heatID, participantID).Scan(&ft.ID, &ft.HeatID, &ft.ParticipantID, &ft.FinishTime, &ft.DNS, &ft.DNF, &ft.CreatedAt, &ft.UpdatedAt)

	return ft, err
}

// DeleteHeat deletes a heat and all its finish times
func (db *DB) DeleteHeat(heatID int64) error {
	// Check if this is the only heat for the event
	var eventID int64
	var heatCount int
	err := db.QueryRow(`
		SELECT event_id FROM heats WHERE id = ?
	`, heatID).Scan(&eventID)
	if err != nil {
		return err
	}

	err = db.QueryRow(`
		SELECT COUNT(*) FROM heats WHERE event_id = ?
	`, eventID).Scan(&heatCount)
	if err != nil {
		return err
	}

	if heatCount <= 1 {
		return fmt.Errorf("cannot delete the last heat - each event must have at least one heat")
	}

	// Delete the heat (finish times will be deleted by CASCADE)
	_, err = db.Exec(`DELETE FROM heats WHERE id = ?`, heatID)
	return err
}

// UpdateHeatStartTime updates the start time for a specific heat
func (db *DB) UpdateHeatStartTime(heatID int64, startTime string) error {
	_, err := db.Exec(`
		UPDATE heats
		SET start_time = ?
		WHERE id = ?
	`, startTime, heatID)
	return err
}

// UpdateHeatDetails updates heat number, name, and optionally start time
func (db *DB) UpdateHeatDetails(heatID int64, heatNumber int, name string, startTime *string) error {
	if startTime != nil {
		_, err := db.Exec(`
			UPDATE heats
			SET heat_number = ?, name = ?, start_time = ?
			WHERE id = ?
		`, heatNumber, name, *startTime, heatID)
		return err
	}
	_, err := db.Exec(`
		UPDATE heats
		SET heat_number = ?, name = ?
		WHERE id = ?
	`, heatNumber, name, heatID)
	return err
}

// GetHeatsWithFinishTimes returns heats that have at least one finish time
func (db *DB) GetHeatsWithFinishTimes(eventID int64) ([]models.Heat, error) {
	rows, err := db.Query(`
		SELECT DISTINCT h.id, h.event_id, h.heat_number, h.name, h.start_time, h.created_at
		FROM heats h
		JOIN heat_finish_times hft ON h.id = hft.heat_id
		WHERE h.event_id = ? AND (hft.finish_time != '' OR hft.dns = 1 OR hft.dnf = 1)
		ORDER BY h.heat_number
	`, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var heats []models.Heat
	for rows.Next() {
		var h models.Heat
		err := rows.Scan(&h.ID, &h.EventID, &h.HeatNumber, &h.Name, &h.StartTime, &h.CreatedAt)
		if err != nil {
			return nil, err
		}
		heats = append(heats, h)
	}

	return heats, nil
}

// GetHeatResults calculates and returns the results for a specific heat
func (db *DB) GetHeatResults(heatID int64) ([]models.HeatResult, error) {
	// Get the heat information
	heat, err := db.GetHeat(heatID)
	if err != nil {
		return nil, err
	}

	// Get the event
	event, err := db.GetEvent(heat.EventID)
	if err != nil {
		return nil, err
	}

	// Get all participants for the event
	participants, err := db.GetEventParticipants(heat.EventID)
	if err != nil {
		return nil, err
	}

	// Get finish times for this heat
	finishTimes, err := db.GetHeatFinishTimes(heatID)
	if err != nil {
		return nil, err
	}

	// Create a map for quick lookup of finish times
	finishTimeMap := make(map[int64]models.HeatFinishTime)
	for _, ft := range finishTimes {
		finishTimeMap[ft.ParticipantID] = ft
	}

	var results []models.HeatResult
	var dnsResults []models.HeatResult
	var dnfResults []models.HeatResult

	// Parse the event start time
	baseStartTime, err := time.Parse("15:04", event.Starttid)
	if err != nil {
		// If the start time is invalid, use midnight as the base
		baseStartTime = time.Date(event.Datum.Year(), event.Datum.Month(), event.Datum.Day(), 0, 0, 0, 0, event.Datum.Location())
	}

	for _, p := range participants {
		// Get finish time for this participant in this heat
		ft, hasFinishTime := finishTimeMap[p.ID]

		// Get sailor and boat information
		sailor, err := db.GetSailor(p.SailorID)
		if err != nil {
			continue
		}

		boat, err := db.GetBoat(p.BoatID)
		if err != nil {
			continue
		}

		// Handle DNS (Did Not Start)
		if hasFinishTime && ft.DNS {
			dnsResult := models.HeatResult{
				Heat:             heat,
				EventParticipant: p,
				Sailor:           sailor,
				Boat:             boat,
				FinishTime:       "-",
				StartTime:        "-",
				ElapsedTime:      "-",
				CorrectedTime:    "-",
				TimeToPrevious:   "-",
				TimeToWinner:     "-",
				TotalPersons:     1 + p.CrewCount,
				DNS:              true,
				DNF:              false,
			}
			dnsResults = append(dnsResults, dnsResult)
			continue
		}

		// Handle DNF (Did Not Finish)
		if hasFinishTime && ft.DNF {
			// Calculate start time for DNF boats
			var startTimeStr string
			if event.IsJaktstart() {
				srsValue := p.SelectedSRSValue
				if p.UseCustomSRSValue {
					srsValue = p.CustomSRSValue
				}
				lowestSRS := db.findLowestSRS(participants)
				multiplier := event.GetJaktstartMultiplier()
				offsetSeconds := calculateStartTimeOffsetWithMultiplier(float64(event.Banlangd), event.Vind, srsValue, lowestSRS, multiplier)
				startTime := baseStartTime.Add(time.Duration(offsetSeconds) * time.Second)
				startTimeStr = startTime.Format("15:04")
			} else {
				startTimeStr = event.Starttid
			}

			dnfResult := models.HeatResult{
				Heat:             heat,
				EventParticipant: p,
				Sailor:           sailor,
				Boat:             boat,
				FinishTime:       "-",
				StartTime:        startTimeStr,
				ElapsedTime:      "-",
				CorrectedTime:    "-",
				TimeToPrevious:   "-",
				TimeToWinner:     "-",
				TotalPersons:     1 + p.CrewCount,
				DNS:              false,
				DNF:              true,
			}
			dnfResults = append(dnfResults, dnfResult)
			continue
		}

		// Skip participants without finish times who are not DNS or DNF
		if !hasFinishTime || ft.FinishTime == "" {
			continue
		}

		// For boats that finished, we need to implement the full calculation
		// This is a placeholder - the full implementation would include
		// all the time calculations from the existing finish_times.go

		result := models.HeatResult{
			Heat:             heat,
			EventParticipant: p,
			Sailor:           sailor,
			Boat:             boat,
			FinishTime:       ft.FinishTime,
			TotalPersons:     1 + p.CrewCount,
			DNS:              false,
			DNF:              false,
		}

		results = append(results, result)
	}

	// TODO: Sort results by corrected time and assign positions and points
	// TODO: Calculate elapsed time, corrected time, etc.

	// Combine all results: finished boats, then DNF, then DNS
	allResults := append(results, dnfResults...)
	allResults = append(allResults, dnsResults...)

	return allResults, nil
}

// GetEventTotalResultsEntypsegling calculates and returns aggregated results across all heats for an entypsegling event
func (db *DB) GetEventTotalResultsEntypsegling(eventID int64) ([]models.TotalResult, error) {
	// Get all heats for the event
	heats, err := db.GetEventHeats(eventID)
	if err != nil {
		return nil, err
	}

	if len(heats) == 0 {
		return nil, fmt.Errorf("no heats found for event %d", eventID)
	}

	// Get the event
	event, err := db.GetEvent(eventID)
	if err != nil {
		return nil, err
	}

	// Get all participants for the event
	participants, err := db.GetEventParticipants(eventID)
	if err != nil {
		return nil, err
	}

	// Create a map to store total results for each participant
	totalResultsMap := make(map[int64]*models.TotalResult)

	// Initialize total results for each participant
	for _, p := range participants {
		sailor, err := db.GetSailor(p.SailorID)
		if err != nil {
			continue
		}

		// For entypsegling events, use placeholder boat with event's boat type
		boat := models.Boat{
			ID:     0,
			Namn:   event.BoatType,
			Battyp: event.BoatType,
			SRS:    0,
		}

		totalResultsMap[p.ID] = &models.TotalResult{
			EventParticipant: p,
			Sailor:           sailor,
			Boat:             boat,
			HeatResults:      []models.HeatResult{},
			TotalPoints:      0,
			TotalPosition:    0,
		}
	}

	// Calculate results for each heat
	for _, heat := range heats {
		heatResults, err := db.GetHeatResultsCompleteEntypsegling(heat.ID)
		if err != nil {
			log.Printf("Error getting results for heat %d: %v", heat.ID, err)
			continue
		}

		// Add heat results to total results
		for _, heatResult := range heatResults {
			if totalResult, exists := totalResultsMap[heatResult.EventParticipant.ID]; exists {
				totalResult.HeatResults = append(totalResult.HeatResults, heatResult)
			}
		}
	}

	// Apply discard logic if enabled
	if event.IsDiscardEnabled() && event.CanDiscardHeats(len(heats)) {
		discardCount := event.GetDiscardCount(len(heats))
		log.Printf("DEBUG DISCARD ENTYPSEGLING: Event %d - totalHeats: %d, discardAfterHeats: %d, discardCount: %d",
			event.ID, len(heats), event.DiscardAfterHeats, discardCount)
		db.applyDiscardLogic(totalResultsMap, discardCount)
	} else {
		// No discarding - just sum all points
		for _, totalResult := range totalResultsMap {
			for _, heatResult := range totalResult.HeatResults {
				totalResult.TotalPoints += heatResult.Points
			}
		}
	}

	// Convert map to slice
	var totalResults []models.TotalResult
	for _, result := range totalResultsMap {
		totalResults = append(totalResults, *result)
	}

	// Sort by total points (lower is better for sailing)
	sort.Slice(totalResults, func(i, j int) bool {
		// If total points are equal, use tie-breaking rules
		if totalResults[i].TotalPoints == totalResults[j].TotalPoints {
			return db.breakTieEntypsegling(totalResults[i], totalResults[j])
		}
		return totalResults[i].TotalPoints < totalResults[j].TotalPoints
	})

	// Assign positions sequentially (no shared positions for entypsegling)
	// The sort already handles tie-breaking according to RRS A8.1 and A8.2
	for i := range totalResults {
		totalResults[i].TotalPosition = i + 1
	}

	return totalResults, nil
}

// breakTieEntypsegling implements tie-breaking rules for entypsegling events
func (db *DB) breakTieEntypsegling(a, b models.TotalResult) bool {
	// Compare individual race results from best to worst scores until a difference is found
	// This follows Racing Rules of Sailing tie-breaking procedures

	// First, try to break tie using only counting heats (non-discarded)
	// This ensures that discarded heats don't affect tie-breaking when counting heats differ
	aCountingResults := make([]float64, 0)
	bCountingResults := make([]float64, 0)
	aAllResults := make([]float64, 0)
	bAllResults := make([]float64, 0)

	for _, heatResult := range a.HeatResults {
		aAllResults = append(aAllResults, heatResult.Points)

		// Check if this heat is discarded
		isDiscarded := false
		for _, discardedHeatID := range a.DiscardedHeats {
			if heatResult.Heat.ID == discardedHeatID {
				isDiscarded = true
				break
			}
		}
		if !isDiscarded {
			aCountingResults = append(aCountingResults, heatResult.Points)
		}
	}

	for _, heatResult := range b.HeatResults {
		bAllResults = append(bAllResults, heatResult.Points)

		// Check if this heat is discarded
		isDiscarded := false
		for _, discardedHeatID := range b.DiscardedHeats {
			if heatResult.Heat.ID == discardedHeatID {
				isDiscarded = true
				break
			}
		}
		if !isDiscarded {
			bCountingResults = append(bCountingResults, heatResult.Points)
		}
	}

	// DEBUG: Log entypsegling tie-breaking details
	log.Printf("DEBUG ENTYPSEGLING TIE-BREAKING: %s (%.1fp) vs %s (%.1fp)",
		a.Sailor.Namn, a.TotalPoints, b.Sailor.Namn, b.TotalPoints)
	log.Printf("DEBUG: %s all results: %v", a.Sailor.Namn, aAllResults)
	log.Printf("DEBUG: %s all results: %v", b.Sailor.Namn, bAllResults)
	log.Printf("DEBUG: %s counting results: %v", a.Sailor.Namn, aCountingResults)
	log.Printf("DEBUG: %s counting results: %v", b.Sailor.Namn, bCountingResults)
	log.Printf("DEBUG: %s discarded heats: %v", a.Sailor.Namn, a.DiscardedHeats)
	log.Printf("DEBUG: %s discarded heats: %v", b.Sailor.Namn, b.DiscardedHeats)

	// DEBUG: Log detailed heat results
	log.Printf("DEBUG: %s detailed heat results:", a.Sailor.Namn)
	for _, hr := range a.HeatResults {
		isDiscarded := false
		for _, discardedHeatID := range a.DiscardedHeats {
			if hr.Heat.ID == discardedHeatID {
				isDiscarded = true
				break
			}
		}
		log.Printf("  Heat %d: %.1fp (discarded: %v)", hr.Heat.ID, hr.Points, isDiscarded)
	}
	log.Printf("DEBUG: %s detailed heat results:", b.Sailor.Namn)
	for _, hr := range b.HeatResults {
		isDiscarded := false
		for _, discardedHeatID := range b.DiscardedHeats {
			if hr.Heat.ID == discardedHeatID {
				isDiscarded = true
				break
			}
		}
		log.Printf("  Heat %d: %.1fp (discarded: %v)", hr.Heat.ID, hr.Points, isDiscarded)
	}

	// Step 1: Try to break tie using only counting heats (non-discarded)
	// This prevents discarded heats from affecting tie-breaking when counting heats differ
	sort.Float64s(aCountingResults)
	sort.Float64s(bCountingResults)

	// Check if counting results are identical
	countingResultsIdentical := len(aCountingResults) == len(bCountingResults)
	if countingResultsIdentical {
		for i := 0; i < len(aCountingResults); i++ {
			if aCountingResults[i] != bCountingResults[i] {
				countingResultsIdentical = false
				break
			}
		}
	}

	if !countingResultsIdentical {
		// Counting heats differ - use them for tie-breaking
		maxCountingLen := len(aCountingResults)
		if len(bCountingResults) > maxCountingLen {
			maxCountingLen = len(bCountingResults)
		}

		for i := 0; i < maxCountingLen; i++ {
			aPoints := 999.0 // Default high value if no result
			bPoints := 999.0

			if i < len(aCountingResults) {
				aPoints = aCountingResults[i]
			}
			if i < len(bCountingResults) {
				bPoints = bCountingResults[i]
			}

			if aPoints != bPoints {
				log.Printf("DEBUG: Tie broken by counting heats at position %d: %s (%.1fp) vs %s (%.1fp)",
					i+1, a.Sailor.Namn, aPoints, b.Sailor.Namn, bPoints)
				return aPoints < bPoints
			}
		}
	}

	// Step 2: Counting heats are identical, go directly to RRS A8.2 (most recent race)
	// This prevents discarded heats from affecting tie-breaking when counting heats are the same
	log.Printf("DEBUG: Counting heats identical, applying RRS A8.2 (most recent race)")

	// RRS A8.2: If still tied after A8.1, compare most recent race result
	// Get most recent heat result for both participants
	aMostRecent := getMostRecentHeatResult(a.HeatResults)
	bMostRecent := getMostRecentHeatResult(b.HeatResults)

	if aMostRecent != bMostRecent {
		log.Printf("DEBUG: RRS A8.2 tie broken on most recent race: %s (%.1fp) vs %s (%.1fp)",
			a.Sailor.Namn, aMostRecent, b.Sailor.Namn, bMostRecent)
		return aMostRecent < bMostRecent
	}

	// RRS A8.2: Compare second most recent, third most recent, etc.
	aReverseResults := getHeatResultsInReverseOrder(a.HeatResults)
	bReverseResults := getHeatResultsInReverseOrder(b.HeatResults)

	maxLenReverse := len(aReverseResults)
	if len(bReverseResults) > maxLenReverse {
		maxLenReverse = len(bReverseResults)
	}

	for i := 0; i < maxLenReverse; i++ {
		aPoints := 999.0 // Default high value if no result
		bPoints := 999.0

		if i < len(aReverseResults) {
			aPoints = aReverseResults[i]
		}
		if i < len(bReverseResults) {
			bPoints = bReverseResults[i]
		}

		if aPoints != bPoints {
			log.Printf("DEBUG: RRS A8.2 tie broken at race %d from end: %s (%.1fp) vs %s (%.1fp)",
				i+1, a.Sailor.Namn, aPoints, b.Sailor.Namn, bPoints)
			return aPoints < bPoints
		}
	}

	// Final tiebreaker: participant ID for consistent ordering
	log.Printf("DEBUG: Final tiebreaker by participant ID: %s (%d) vs %s (%d)",
		a.Sailor.Namn, a.EventParticipant.ID, b.Sailor.Namn, b.EventParticipant.ID)
	return a.EventParticipant.ID < b.EventParticipant.ID
}

// GetEventTotalResults calculates and returns aggregated results across all heats for an event
func (db *DB) GetEventTotalResults(eventID int64) ([]models.TotalResult, error) {
	// Get the event to check if it's entypsegling
	event, err := db.GetEvent(eventID)
	if err != nil {
		return nil, err
	}

	// For entypsegling events, use placement-based results
	if event.Entypsegling {
		return db.GetEventTotalResultsEntypsegling(eventID)
	}

	// Get all heats for the event
	heats, err := db.GetEventHeats(eventID)
	if err != nil {
		return nil, err
	}

	if len(heats) == 0 {
		return nil, fmt.Errorf("no heats found for event %d", eventID)
	}

	// Get all participants for the event
	participants, err := db.GetEventParticipants(eventID)
	if err != nil {
		return nil, err
	}

	// Create a map to store total results for each participant
	totalResultsMap := make(map[int64]*models.TotalResult)

	// Initialize total results for each participant
	for _, p := range participants {
		sailor, err := db.GetSailor(p.SailorID)
		if err != nil {
			continue
		}

		boat, err := db.GetBoat(p.BoatID)
		if err != nil {
			continue
		}

		totalResultsMap[p.ID] = &models.TotalResult{
			EventParticipant: p,
			Sailor:           sailor,
			Boat:             boat,
			HeatResults:      []models.HeatResult{},
			TotalPoints:      0,
			TotalPosition:    0,
		}
	}

	// Calculate results for each heat and accumulate points
	for _, heat := range heats {
		heatResults, err := db.GetHeatResultsComplete(heat.ID)
		if err != nil {
			log.Printf("Error getting results for heat %d: %v", heat.ID, err)
			continue
		}

		// Add heat results to total results
		for _, heatResult := range heatResults {
			if totalResult, exists := totalResultsMap[heatResult.EventParticipant.ID]; exists {
				totalResult.HeatResults = append(totalResult.HeatResults, heatResult)
			}
		}
	}

	// Apply discard logic if enabled
	if event.IsDiscardEnabled() && event.CanDiscardHeats(len(heats)) {
		discardCount := event.GetDiscardCount(len(heats))
		log.Printf("DEBUG DISCARD: Event %d - totalHeats: %d, discardAfterHeats: %d, discardCount: %d",
			event.ID, len(heats), event.DiscardAfterHeats, discardCount)
		db.applyDiscardLogic(totalResultsMap, discardCount)
	} else {
		// No discarding - just sum all points
		for _, totalResult := range totalResultsMap {
			for _, heatResult := range totalResult.HeatResults {
				totalResult.TotalPoints += heatResult.Points
			}
		}
	}

	// Convert map to slice and sort by total points (lowest points wins)
	var totalResults []models.TotalResult
	for _, totalResult := range totalResultsMap {
		totalResults = append(totalResults, *totalResult)
	}

	// Sort by total points (ascending - lowest points wins)
	// With tiebreaker for boats with equal total points
	sort.Slice(totalResults, func(i, j int) bool {
		// Primary sort: total points (lower is better)
		if totalResults[i].TotalPoints != totalResults[j].TotalPoints {
			return totalResults[i].TotalPoints < totalResults[j].TotalPoints
		}

		// Use the same tie-breaking logic as entypsegling for consistency
		return db.breakTieEntypsegling(totalResults[i], totalResults[j])
	})

	// Assign overall positions with shared positions for complete ties
	db.assignSharedPositions(totalResults)

	return totalResults, nil
}

// assignSharedPositions assigns positions to results, handling shared positions for complete ties
func (db *DB) assignSharedPositions(totalResults []models.TotalResult) {
	if len(totalResults) == 0 {
		return
	}

	currentPosition := 1
	for i := 0; i < len(totalResults); i++ {
		// Find all boats that are completely tied with the current boat
		tiedGroup := []int{i} // Start with current boat

		// Look ahead to find all boats with identical results
		for j := i + 1; j < len(totalResults); j++ {
			if db.areCompletelyTied(totalResults[i], totalResults[j]) {
				tiedGroup = append(tiedGroup, j)
			} else {
				break // No more ties in this group
			}
		}

		// Assign the same position to all boats in the tied group
		for _, idx := range tiedGroup {
			totalResults[idx].TotalPosition = currentPosition
		}

		// Move to next position, skipping positions occupied by tied boats
		currentPosition += len(tiedGroup)

		// Skip ahead past the tied group
		i += len(tiedGroup) - 1
	}
}

// areCompletelyTied checks if two boats are completely tied (same total points AND same individual race results)
func (db *DB) areCompletelyTied(a, b models.TotalResult) bool {
	// First check if total points are different
	if a.TotalPoints != b.TotalPoints {
		return false
	}

	// Get all race results for both participants
	// For complete ties, we compare ALL results including discarded heats
	aResults := make([]float64, 0)
	bResults := make([]float64, 0)

	for _, heatResult := range a.HeatResults {
		aResults = append(aResults, heatResult.Points)
	}
	for _, heatResult := range b.HeatResults {
		bResults = append(bResults, heatResult.Points)
	}

	// If different number of races, they can't be completely tied
	if len(aResults) != len(bResults) {
		return false
	}

	// Sort results (best to worst, i.e., lowest to highest points)
	sort.Float64s(aResults)
	sort.Float64s(bResults)

	// Compare all results - they must be identical for a complete tie
	for i := 0; i < len(aResults); i++ {
		if aResults[i] != bResults[i] {
			return false
		}
	}

	// If we get here, boats are completely tied
	return true
}

// applyDiscardLogic applies discard logic to total results, discarding the worst results for each participant
func (db *DB) applyDiscardLogic(totalResultsMap map[int64]*models.TotalResult, discardCount int) {
	for _, totalResult := range totalResultsMap {
		// Calculate the actual discard count for this participant
		actualDiscardCount := discardCount
		if len(totalResult.HeatResults) <= discardCount {
			// If we have fewer or equal heats than discard count, discard all but one
			// (we need at least one result to count)
			if len(totalResult.HeatResults) > 1 {
				actualDiscardCount = len(totalResult.HeatResults) - 1
			} else {
				actualDiscardCount = 0
			}
		}

		if actualDiscardCount > 0 {
			// Sort heat results by points (worst first - highest points)
			// When points are equal, discard the earliest (oldest) heat according to RRS
			heatResults := make([]models.HeatResult, len(totalResult.HeatResults))
			copy(heatResults, totalResult.HeatResults)

			sort.Slice(heatResults, func(i, j int) bool {
				// Primary sort: worst points first (highest points)
				if heatResults[i].Points != heatResults[j].Points {
					return heatResults[i].Points > heatResults[j].Points
				}
				// Secondary sort: when points are equal, earliest heat first (lowest heat number)
				// This follows RRS: "Ties shall be broken in favour of the race or races with the worst scores earliest in the series"
				return heatResults[i].Heat.HeatNumber < heatResults[j].Heat.HeatNumber
			})

			// Mark the worst results as discarded
			totalResult.DiscardedHeats = make([]int64, 0, actualDiscardCount)
			for i := 0; i < actualDiscardCount && i < len(heatResults); i++ {
				totalResult.DiscardedHeats = append(totalResult.DiscardedHeats, heatResults[i].Heat.ID)
			}
		}

		// Calculate total points excluding discarded heats
		totalResult.TotalPoints = 0
		for _, heatResult := range totalResult.HeatResults {
			isDiscarded := false
			for _, discardedHeatID := range totalResult.DiscardedHeats {
				if heatResult.Heat.ID == discardedHeatID {
					isDiscarded = true
					break
				}
			}
			if !isDiscarded {
				totalResult.TotalPoints += heatResult.Points
			}
		}
	}
}

// GetHeatResultsCompleteEntypsegling calculates complete results for a specific entypsegling heat based on placements
func (db *DB) GetHeatResultsCompleteEntypsegling(heatID int64) ([]models.HeatResult, error) {
	// Get the heat information
	heat, err := db.GetHeat(heatID)
	if err != nil {
		return nil, err
	}

	// Get the event
	event, err := db.GetEvent(heat.EventID)
	if err != nil {
		return nil, err
	}

	// Get all participants for the event
	participants, err := db.GetEventParticipants(heat.EventID)
	if err != nil {
		return nil, err
	}

	// Get finish times/placements for this heat
	finishTimes, err := db.GetHeatFinishTimes(heatID)
	if err != nil {
		return nil, err
	}

	// Create a map for quick lookup of placements
	placementMap := make(map[int64]models.HeatFinishTime)
	for _, ft := range finishTimes {
		placementMap[ft.ParticipantID] = ft
	}

	var results []models.HeatResult
	var dnsResults []models.HeatResult
	var dnfResults []models.HeatResult

	for _, p := range participants {
		// Get placement for this participant in this heat
		ft, hasPlacement := placementMap[p.ID]

		// Get sailor information
		sailor, err := db.GetSailor(p.SailorID)
		if err != nil {
			continue
		}

		// For entypsegling events, we don't need boat information
		// Use a placeholder boat with the event's boat type
		boat := models.Boat{
			ID:     0,
			Namn:   event.BoatType,
			Battyp: event.BoatType,
			SRS:    0,
		}

		// Handle DNS (Did Not Start)
		if hasPlacement && ft.DNS {
			dnsResult := models.HeatResult{
				Heat:             heat,
				EventParticipant: p,
				Sailor:           sailor,
				Boat:             boat,
				FinishTime:       "-",
				StartTime:        "-",
				ElapsedTime:      "-",
				CorrectedTime:    "-",
				TimeToPrevious:   "-",
				TimeToWinner:     "-",
				TotalPersons:     1 + p.CrewCount,
				Position:         0,
				Points:           0,
				DNS:              true,
				DNF:              false,
			}
			dnsResults = append(dnsResults, dnsResult)
			continue
		}

		// Handle DNF (Did Not Finish)
		if hasPlacement && ft.DNF {
			dnfResult := models.HeatResult{
				Heat:             heat,
				EventParticipant: p,
				Sailor:           sailor,
				Boat:             boat,
				FinishTime:       "-",
				StartTime:        "-",
				ElapsedTime:      "-",
				CorrectedTime:    "-",
				TimeToPrevious:   "-",
				TimeToWinner:     "-",
				TotalPersons:     1 + p.CrewCount,
				Position:         0,
				Points:           0,
				DNS:              false,
				DNF:              true,
			}
			dnfResults = append(dnfResults, dnfResult)
			continue
		}

		// Skip participants without placement (not yet placed)
		if !hasPlacement || ft.Placement == 0 {
			continue
		}

		// Create result for participants with placements
		result := models.HeatResult{
			Heat:             heat,
			EventParticipant: p,
			Sailor:           sailor,
			Boat:             boat,
			FinishTime:       "-", // Not used for entypsegling
			StartTime:        "-", // Not used for entypsegling
			ElapsedTime:      "-", // Not used for entypsegling
			CorrectedTime:    "-", // Not used for entypsegling
			TimeToPrevious:   "-", // Not used for entypsegling
			TimeToWinner:     "-", // Not used for entypsegling
			TotalPersons:     1 + p.CrewCount,
			Position:         ft.Placement,
			Points:           float64(ft.Placement), // Points = placement for entypsegling
			DNS:              false,
			DNF:              false,
		}

		results = append(results, result)
	}

	// Sort results by placement (position)
	sort.Slice(results, func(i, j int) bool {
		return results[i].Position < results[j].Position
	})

	// Calculate points for tied finishes
	// ⛵ Tied Finish in a Race (Same Placement)
	// If two or more boats finish a race in a tie for the same position,
	// they are each awarded the average of the points for those positions.
	// Formula: Points = (sum of the places they occupy) ÷ (number of boats tied)
	db.calculateTiedFinishPoints(results)

	// Calculate DNS and DNF points based on number of participants who actually sailed
	totalParticipants := len(results) + len(dnfResults) + len(dnsResults)

	// DEBUG: Log DNS/DNF point calculation
	log.Printf("DEBUG DNS/DNF POINTS: Heat %d - results: %d, dnfResults: %d, dnsResults: %d, totalParticipants: %d",
		heatID, len(results), len(dnfResults), len(dnsResults), totalParticipants)

	// DNS boats get points equal to total participants + 1
	for i := range dnsResults {
		dnsResults[i].Points = float64(totalParticipants + 1)
		log.Printf("DEBUG: DNS participant %s gets %.1f points", dnsResults[i].Sailor.Namn, dnsResults[i].Points)
	}

	// DNF boats get points equal to one more than the last boat that finished
	dnfPoints := len(results) + 1
	for i := range dnfResults {
		dnfResults[i].Points = float64(dnfPoints)
		log.Printf("DEBUG: DNF participant %s gets %.1f points", dnfResults[i].Sailor.Namn, dnfResults[i].Points)
	}

	// Combine all results: finished boats (sorted by placement), then DNF, then DNS
	allResults := append(results, dnfResults...)
	allResults = append(allResults, dnsResults...)

	return allResults, nil
}

// calculateTiedFinishPoints calculates points for tied finishes in entypsegling events
// ⛵ Tied Finish in a Race (Same Placement)
// If two or more boats finish a race in a tie for the same position,
// they are each awarded the average of the points for those positions.
// Formula: Points = (sum of the places they occupy) ÷ (number of boats tied)
func (db *DB) calculateTiedFinishPoints(results []models.HeatResult) {
	if len(results) == 0 {
		return
	}

	// Group results by position to find ties
	positionGroups := make(map[int][]int) // position -> slice of result indices
	for i, result := range results {
		positionGroups[result.Position] = append(positionGroups[result.Position], i)
	}

	// Calculate points for each position group
	currentPoints := 1
	for position := 1; position <= len(results); position++ {
		indices, exists := positionGroups[position]
		if !exists {
			continue
		}

		numTied := len(indices)
		if numTied == 1 {
			// No tie, assign current points
			results[indices[0]].Points = float64(currentPoints)
			currentPoints++
		} else {
			// Tie: calculate average points
			// Sum of places they occupy: currentPoints + (currentPoints+1) + ... + (currentPoints+numTied-1)
			sumOfPlaces := 0
			for i := 0; i < numTied; i++ {
				sumOfPlaces += currentPoints + i
			}
			averagePoints := float64(sumOfPlaces) / float64(numTied)

			// Assign the average points to all tied boats with one decimal precision
			for _, idx := range indices {
				results[idx].Points = averagePoints
			}

			// Move to next available points position
			currentPoints += numTied
		}
	}
}

// GetHeatResultsComplete calculates complete results for a specific heat with positions and points
func (db *DB) GetHeatResultsComplete(heatID int64) ([]models.HeatResult, error) {
	// Get the heat information
	heat, err := db.GetHeat(heatID)
	if err != nil {
		return nil, err
	}

	// Get the event
	event, err := db.GetEvent(heat.EventID)
	if err != nil {
		return nil, err
	}

	// For entypsegling events, use placement-based results
	if event.Entypsegling {
		return db.GetHeatResultsCompleteEntypsegling(heatID)
	}

	// Get all participants for the event
	participants, err := db.GetEventParticipants(heat.EventID)
	if err != nil {
		return nil, err
	}

	// Get finish times for this heat
	finishTimes, err := db.GetHeatFinishTimes(heatID)
	if err != nil {
		return nil, err
	}

	// Create a map for quick lookup of finish times
	finishTimeMap := make(map[int64]models.HeatFinishTime)
	for _, ft := range finishTimes {
		finishTimeMap[ft.ParticipantID] = ft
	}

	var results []models.HeatResult
	var dnsResults []models.HeatResult
	var dnfResults []models.HeatResult

	// Use heat start time if available, otherwise use event start time
	var baseStartTimeStr string
	if heat.StartTime != "" {
		baseStartTimeStr = heat.StartTime
	} else {
		baseStartTimeStr = event.Starttid
	}

	// Parse the base start time
	baseStartTime, err := time.Parse("15:04", baseStartTimeStr)
	if err != nil {
		// If the start time is invalid, use midnight as the base
		baseStartTime = time.Date(event.Datum.Year(), event.Datum.Month(), event.Datum.Day(), 0, 0, 0, 0, event.Datum.Location())
	}

	for _, p := range participants {
		// Get finish time for this participant in this heat
		ft, hasFinishTime := finishTimeMap[p.ID]

		// Get sailor and boat information
		sailor, err := db.GetSailor(p.SailorID)
		if err != nil {
			continue
		}

		boat, err := db.GetBoat(p.BoatID)
		if err != nil {
			continue
		}

		// Handle DNS (Did Not Start)
		if hasFinishTime && ft.DNS {
			dnsResult := models.HeatResult{
				Heat:             heat,
				EventParticipant: p,
				Sailor:           sailor,
				Boat:             boat,
				FinishTime:       "-",
				StartTime:        "-",
				ElapsedTime:      "-",
				CorrectedTime:    "-",
				TimeToPrevious:   "-",
				TimeToWinner:     "-",
				TotalPersons:     1 + p.CrewCount,
				DNS:              true,
				DNF:              false,
				Position:         0, // Will be set later
				Points:           0, // Will be set later
			}
			dnsResults = append(dnsResults, dnsResult)
			continue
		}

		// Handle DNF (Did Not Finish)
		if hasFinishTime && ft.DNF {
			// Calculate start time for DNF boats
			var startTimeStr string
			if event.IsJaktstart() {
				srsValue := p.SelectedSRSValue
				if p.UseCustomSRSValue {
					srsValue = p.CustomSRSValue
				}
				lowestSRS := db.findLowestSRS(participants)
				multiplier := event.GetJaktstartMultiplier()
				offsetSeconds := calculateStartTimeOffsetWithMultiplier(float64(event.Banlangd), event.Vind, srsValue, lowestSRS, multiplier)
				startTime := baseStartTime.Add(time.Duration(offsetSeconds) * time.Second)
				startTimeStr = startTime.Format("15:04")
			} else {
				startTimeStr = baseStartTimeStr
			}

			dnfResult := models.HeatResult{
				Heat:             heat,
				EventParticipant: p,
				Sailor:           sailor,
				Boat:             boat,
				FinishTime:       "-",
				StartTime:        startTimeStr,
				ElapsedTime:      "-",
				CorrectedTime:    "-",
				TimeToPrevious:   "-",
				TimeToWinner:     "-",
				TotalPersons:     1 + p.CrewCount,
				DNS:              false,
				DNF:              true,
				Position:         0, // Will be set later
				Points:           0, // Will be set later
			}
			dnfResults = append(dnfResults, dnfResult)
			continue
		}

		// Skip participants without finish times who are not DNS or DNF
		if !hasFinishTime || ft.FinishTime == "" {
			continue
		}

		// For boats that finished, calculate all the timing details
		// This uses the same logic as GetEventResults but adapted for heats

		// Calculate start time
		var startTimeStr string
		if event.IsJaktstart() {
			srsValue := p.SelectedSRSValue
			if p.UseCustomSRSValue {
				srsValue = p.CustomSRSValue
			}
			lowestSRS := db.findLowestSRS(participants)
			multiplier := event.GetJaktstartMultiplier()
			offsetSeconds := calculateStartTimeOffsetWithMultiplier(float64(event.Banlangd), event.Vind, srsValue, lowestSRS, multiplier)
			startTime := baseStartTime.Add(time.Duration(offsetSeconds) * time.Second)
			startTimeStr = startTime.Format("15:04")
		} else {
			startTimeStr = baseStartTimeStr
		}

		// Parse finish time
		finishTime, err := time.Parse("15:04:05", ft.FinishTime)
		if err != nil {
			// Try parsing without seconds
			finishTime, err = time.Parse("15:04", ft.FinishTime)
			if err != nil {
				continue
			}
		}

		// Parse start time for calculations
		startTime, err := time.Parse("15:04", startTimeStr)
		if err != nil {
			continue
		}

		// Calculate elapsed time
		elapsedDuration := finishTime.Sub(startTime)
		elapsedSeconds := int(elapsedDuration.Seconds())
		elapsedHours := elapsedSeconds / 3600
		elapsedMinutes := (elapsedSeconds % 3600) / 60
		elapsedSecondsRemainder := elapsedSeconds % 60
		elapsedTimeStr := fmt.Sprintf("%02d:%02d:%02d", elapsedHours, elapsedMinutes, elapsedSecondsRemainder)

		// Calculate corrected time
		srsValue := p.SelectedSRSValue
		if p.UseCustomSRSValue {
			srsValue = p.CustomSRSValue
		}

		correctedSecondsFloat := float64(elapsedSeconds) * srsValue
		correctedSeconds := int(correctedSecondsFloat)
		correctedHours := correctedSeconds / 3600
		correctedMinutes := (correctedSeconds % 3600) / 60
		correctedSecondsRemainder := correctedSeconds % 60
		correctedTimeStr := fmt.Sprintf("%02d:%02d:%02d", correctedHours, correctedMinutes, correctedSecondsRemainder)

		result := models.HeatResult{
			Heat:                  heat,
			EventParticipant:      p,
			Sailor:                sailor,
			Boat:                  boat,
			FinishTime:            ft.FinishTime,
			StartTime:             startTimeStr,
			ElapsedTime:           elapsedTimeStr,
			CorrectedTime:         correctedTimeStr,
			ElapsedSeconds:        elapsedSeconds,
			CorrectedSeconds:      correctedSeconds,
			CorrectedSecondsFloat: correctedSecondsFloat,
			TimeToPrevious:        "", // Will be calculated after sorting
			TimeToWinner:          "", // Will be calculated after sorting
			TotalPersons:          1 + p.CrewCount,
			DNS:                   false,
			DNF:                   false,
			Position:              0, // Will be set after sorting
			Points:                0, // Will be set after sorting
		}

		results = append(results, result)
	}

	// Sort results by corrected time (ascending)
	sort.Slice(results, func(i, j int) bool {
		return results[i].CorrectedSecondsFloat < results[j].CorrectedSecondsFloat
	})

	// Assign positions and calculate time differences for finished boats
	for i := range results {
		results[i].Position = i + 1
		results[i].Points = float64(i + 1) // Points = position in sailing

		if i == 0 {
			// Winner has no time differences
			results[i].TimeToWinner = "00:00:00"
			results[i].TimeToPrevious = "00:00:00"
		} else {
			// Time to winner
			diffToWinner := results[i].CorrectedSeconds - results[0].CorrectedSeconds
			winnerHours := diffToWinner / 3600
			winnerMinutes := (diffToWinner % 3600) / 60
			winnerSeconds := diffToWinner % 60
			results[i].TimeToWinner = fmt.Sprintf("%02d:%02d:%02d", winnerHours, winnerMinutes, winnerSeconds)

			// Time to previous
			diffToPrevious := results[i].CorrectedSeconds - results[i-1].CorrectedSeconds
			previousHours := diffToPrevious / 3600
			previousMinutes := (diffToPrevious % 3600) / 60
			previousSeconds := diffToPrevious % 60
			results[i].TimeToPrevious = fmt.Sprintf("%02d:%02d:%02d", previousHours, previousMinutes, previousSeconds)
		}
	}

	// Calculate DNS and DNF points according to Racing Rules of Sailing (RRS) Appendix A
	totalParticipants := len(results) + len(dnfResults) + len(dnsResults)

	// DNF boats get points equal to one more than the last boat that finished
	dnfPoints := len(results) + 1
	for i := range dnfResults {
		dnfResults[i].Position = 0 // DNF boats don't get a position number
		dnfResults[i].Points = float64(dnfPoints)
	}

	// DNS boats get points equal to total participants + 1
	for i := range dnsResults {
		dnsResults[i].Position = 0 // DNS boats don't get a position number
		dnsResults[i].Points = float64(totalParticipants + 1)
	}

	// Combine all results: finished boats, then DNF, then DNS
	allResults := append(results, dnfResults...)
	allResults = append(allResults, dnsResults...)

	return allResults, nil
}

// getMostRecentHeatResult returns the points from the most recent heat (highest heat number)
func getMostRecentHeatResult(heatResults []models.HeatResult) float64 {
	if len(heatResults) == 0 {
		return 999.0 // High value if no results
	}

	// Find the heat with the highest heat number (most recent)
	mostRecentResult := heatResults[0]
	for _, result := range heatResults {
		if result.Heat.HeatNumber > mostRecentResult.Heat.HeatNumber {
			mostRecentResult = result
		}
	}
	return mostRecentResult.Points
}

// getHeatResultsInReverseOrder returns heat points in reverse chronological order (most recent first)
func getHeatResultsInReverseOrder(heatResults []models.HeatResult) []float64 {
	if len(heatResults) == 0 {
		return []float64{}
	}

	// Sort heat results by heat number in descending order (most recent first)
	sortedResults := make([]models.HeatResult, len(heatResults))
	copy(sortedResults, heatResults)

	sort.Slice(sortedResults, func(i, j int) bool {
		return sortedResults[i].Heat.HeatNumber > sortedResults[j].Heat.HeatNumber
	})

	// Extract points in reverse chronological order
	points := make([]float64, len(sortedResults))
	for i, result := range sortedResults {
		points[i] = result.Points
	}

	return points
}

// getFinishCounts returns an array where index 0 = count of 1st places, index 1 = count of 2nd places, etc.
func getFinishCounts(heatResults []models.HeatResult) []int {
	if len(heatResults) == 0 {
		return []int{}
	}

	// Find the maximum position to determine array size
	maxPosition := 0
	for _, result := range heatResults {
		if result.Position > maxPosition {
			maxPosition = result.Position
		}
	}

	// Create array to count finishes at each position
	finishCounts := make([]int, maxPosition)

	// Count finishes at each position
	for _, result := range heatResults {
		if result.Position > 0 && result.Position <= maxPosition {
			finishCounts[result.Position-1]++ // Convert to 0-based index
		}
	}

	return finishCounts
}

// getFinishCountsExcludingDiscarded returns an array where index 0 = count of 1st places, index 1 = count of 2nd places, etc.
// Only considers non-discarded heats for tie-breaking according to Racing Rules of Sailing
func getFinishCountsExcludingDiscarded(heatResults []models.HeatResult, discardedHeats []int64) []int {
	if len(heatResults) == 0 {
		return []int{}
	}

	// Filter out discarded heats
	var nonDiscardedResults []models.HeatResult
	for _, result := range heatResults {
		isDiscarded := false
		for _, discardedHeatID := range discardedHeats {
			if result.Heat.ID == discardedHeatID {
				isDiscarded = true
				break
			}
		}
		if !isDiscarded {
			nonDiscardedResults = append(nonDiscardedResults, result)
		}
	}

	return getFinishCounts(nonDiscardedResults)
}

// getMostRecentHeatResultExcludingDiscarded returns the points from the most recent non-discarded heat
func getMostRecentHeatResultExcludingDiscarded(heatResults []models.HeatResult, discardedHeats []int64) float64 {
	if len(heatResults) == 0 {
		return 999.0 // High value if no results
	}

	// Filter out discarded heats
	var nonDiscardedResults []models.HeatResult
	for _, result := range heatResults {
		isDiscarded := false
		for _, discardedHeatID := range discardedHeats {
			if result.Heat.ID == discardedHeatID {
				isDiscarded = true
				break
			}
		}
		if !isDiscarded {
			nonDiscardedResults = append(nonDiscardedResults, result)
		}
	}

	return getMostRecentHeatResult(nonDiscardedResults)
}

// getHeatResultsInReverseOrderExcludingDiscarded returns non-discarded heat points in reverse chronological order
func getHeatResultsInReverseOrderExcludingDiscarded(heatResults []models.HeatResult, discardedHeats []int64) []float64 {
	if len(heatResults) == 0 {
		return []float64{}
	}

	// Filter out discarded heats
	var nonDiscardedResults []models.HeatResult
	for _, result := range heatResults {
		isDiscarded := false
		for _, discardedHeatID := range discardedHeats {
			if result.Heat.ID == discardedHeatID {
				isDiscarded = true
				break
			}
		}
		if !isDiscarded {
			nonDiscardedResults = append(nonDiscardedResults, result)
		}
	}

	return getHeatResultsInReverseOrder(nonDiscardedResults)
}
